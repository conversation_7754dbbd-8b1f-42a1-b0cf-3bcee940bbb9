// 定义 EMP 映射关系
def mapping = [
    EMP09S968: 'oc101',
    EMP08S968: 'oc102', 
    EMP01S968: 'oc103',
    EMP04S968: 'oc104',
    EMP02S968: 'oc105',
    EMP07S968: 'oc106',
    EMP10S968: 'oc107'
]

// 构造有效项并排序（按原始键顺序保排名）
def validItems = mapping.keySet().collect { key ->
    [key: key, value: context?."$it"]
}.findAll { 
    it.value != null && it.value != '' && it.value != -1 && it.value != '-1' 
}

if (validItems.isEmpty()) {
    return [JOBPRO01: '-1', flag: 0]
}

// 找出最大值，返回排名最前的那个key对应的oc字段
def maxVal = validItems.max { it.value }?.value
def bestMatch = validItems.find { it.value == maxVal }

return [JOBPRO01: mapping[bestMatch.key], flag: 1]