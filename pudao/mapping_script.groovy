def processMapping(emp09s968, emp08s968, emp01s968, emp04s968, emp02s968, emp07s968, emp10s968) {
// 映射关系定义
def mapping = [
    'EMP09S968': 'oc101',
    'EMP08S968': 'oc102', 
    'EMP01S968': 'oc103',
    'EMP04S968': 'oc104',
    'EMP02S968': 'oc105',
    'EMP07S968': 'oc106',
    'EMP10S968': 'oc107'
]

// 输入值数组（按照排名顺序）
def inputValues = [
    [value: emp09s968, key: 'EMP09S968'],
    [value: emp08s968, key: 'EMP08S968'],
    [value: emp01s968, key: 'EMP01S968'], 
    [value: emp04s968, key: 'EMP04S968'],
    [value: emp02s968, key: 'EMP02S968'],
    [value: emp07s968, key: 'EMP07S968'],
    [value: emp10s968, key: 'EMP10S968']
]

// 过滤掉空值和-1的值
def validValues = inputValues.findAll { item ->
    item.value != null && item.value != '' && item.value != -1 && item.value != '-1'
}

// 如果没有有效值，返回默认结果
if (validValues.isEmpty()) {
    return [JOBPRO01: '-1', flag: 0]
}

// 找到最大值
def maxValue = validValues.max { it.value }?.value

// 找到所有等于最大值的项，取排名靠前的（数组索引最小的）
def maxItem = validValues.find { it.value == maxValue }

// 获取对应的oc字段
def ocField = mapping[maxItem.key]

return [JOBPRO01: ocField, flag: 1]
}

// 测试用例
println "=== 测试用例 ==="

// 测试1: 所有值都为空
println "测试1 - 所有值为空:"
def result1 = processMapping(null, null, null, null, null, null, null)
println "结果: ${result1.JOBPRO01}, flag=${result1.flag}"

// 测试2: 所有值都为-1
println "\n测试2 - 所有值为-1:"
def result2 = processMapping(-1, -1, -1, -1, -1, -1, -1)
println "结果: ${result2.JOBPRO01}, flag=${result2.flag}"

// 测试3: 有有效值的情况
println "\n测试3 - 有有效值:"
def result3 = processMapping(10, 5, 15, 8, 20, 3, 12)
println "结果: ${result3.JOBPRO01}, flag=${result3.flag}"

// 测试4: 有重复最大值的情况（取排名靠前的）
println "\n测试4 - 有重复最大值:"
def result4 = processMapping(20, 15, 20, 10, 18, 20, 12)
println "结果: ${result4.JOBPRO01}, flag=${result4.flag}"

// 测试5: 混合空值和有效值
println "\n测试5 - 混合空值和有效值:"
def result5 = processMapping(null, 8, -1, 15, '', 12, 10)
println "结果: ${result5.JOBPRO01}, flag=${result5.flag}"

// 如果需要通过命令行参数调用，请使用以下示例：
// groovy mapping_script.groovy 10 5 15 8 20 3 12
if (args.length >= 7) {
    try {
        def result = processMapping(
            parseValue(args[0]),
            parseValue(args[1]),
            parseValue(args[2]),
            parseValue(args[3]),
            parseValue(args[4]),
            parseValue(args[5]),
            parseValue(args[6])
        )
        println "\n命令行结果: ${result.JOBPRO01}, flag=${result.flag}"
    } catch (Exception e) {
        println "参数解析错误: ${e.message}"
        println "用法: groovy mapping_script.groovy <emp09s968> <emp08s968> <emp01s968> <emp04s968> <emp02s968> <emp07s968> <emp10s968>"
    }
}

def parseValue(String value) {
    if (value == null || value == 'null' || value == '' || value == '-1') {
        return null
    }
    try {
        return Integer.parseInt(value)
    } catch (NumberFormatException e) {
        return null
    }
} 