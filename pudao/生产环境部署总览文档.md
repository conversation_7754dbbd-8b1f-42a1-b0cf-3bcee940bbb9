# 生产环境部署总览文档

## 1. 架构与服务器规划

本文档旨在详细记录一个由10台服务器构成的、高可用的生产环境的完整部署流程。该环境经过精心设计，将服务按角色划分为三个独立的层级，并最终将运维组件纳入 Swarm 集群进行统一管理，以确保职责清晰、风险隔离和高性能。

#### 1.1 服务器角色分配

- **中间件集群 (3台)**: 负责提供高可用的消息、协调和缓存服务。
    - `***********` (middleware-01)
    - `***********` (middleware-02)
    - `***********` (middleware-03)
- **数据层 (3台)**: 承载核心数据存储和数据分析。
    - `***********` (mysql-master): MySQL 主库节点
    - `***********` (mysql-slave): MySQL 从库节点
    - `***********` (clickhouse-01): ClickHouse 数据分析节点 & MHA 备用管理节点
- **应用与管理集群 (4台)**: 构成 Docker Swarm 集群。其中3台为应用节点，1台为专用的运维节点。
    - `************` (ops-node-01): **Swarm Manager** (专用运维节点，运行Loki, Grafana, Prometheus) & 独立运维组件 (MHA 主Manager, Docker Registry)
    - `************` (app-node-01): **Swarm Manager** (运行业务应用)
    - `************` (app-node-02): **Swarm Manager** (运行业务应用)
    - `************` (app-node-03): **Swarm Manager** (运行业务应用)

## 2. 全局准备与规范

#### 2.1 Hosts 文件配置

为保证所有服务间能通过主机名进行稳定通信，需要在**所有10台服务器**的 `/etc/hosts` 文件中添加以下记录：

```
# 中间件集群
***********    middleware-01
***********    middleware-02
***********    middleware-03

# 数据层
***********    mysql-master
***********    mysql-slave
***********    clickhouse-01

# 应用与管理集群
************   ops-node-01 docker-registry.local # 同时作为运维节点和镜像仓库
************   app-node-01
************   app-node-02
************   app-node-03
```

#### 2.2 离线环境准备

由于生产环境受限，所有软件包都需要提前在有网环境中下载，然后上传到服务器。

## 3. 核心基础设施部署

#### 3.1 Docker & Docker Swarm 集群

- **目标节点**: `.11`, `.14`, `.15`, `.10` (全部4台)

- **步骤**:

    1. **安装 Docker**: 在四台节点上使用离线包 `yum localinstall *.rpm` 安装 Docker Engine。

    2. **启动 Docker**: `sudo systemctl enable --now docker`。

    3. **初始化 Swarm**: 在 `.11` 节点上执行 `docker swarm init --advertise-addr ************`。

    4. **加入 Manager 节点**: 在 `.11` 上执行 `docker swarm join-token manager` 获取令牌，然后在 `.14`, `.15`, `.10` 上执行 join 命令，将它们全部作为 Manager 加入集群。

    5. **为运维节点打标签**: 在主 Manager (`.11`) 上执行 `docker node update --label-add ops=true <NODE_ID_OF_************>`。

    6. **创建共享网络**: 在主 Manager (`.11`) 上手动创建两个外部网络，供不同堆栈共享。

        ```
        docker network create --driver=overlay app-net
        docker network create --driver=overlay monitoring-net
        ```

#### 3.2 私有镜像仓库 (Docker Registry)

- **目标节点**: `.10`
- **部署方式**: **保持独立**，使用独立的 `docker compose` 进行部署，不纳入 Swarm 管理，以避免“鸡生蛋”问题。
- **步骤**:
    1. 加载 `registry:2.8.3` 镜像。
    2. 创建 `/data/docker-registry` 目录及配置文件。
    3. 执行 `sudo docker compose up -d` 启动。
    4. 在所有 Swarm 节点上配置 `/etc/docker/daemon.json` 以信任此仓库。

## 4. 高可用数据层部署

#### 4.1 MySQL 5.7 主从复制

- **目标节点**: Master: `.7`, Slave: `.8`
- **部署要点**:
    - 卸载冲突的 `mariadb-libs`。
    - 使用生产级 `/etc/my.cnf`，**手动指定** Master/Slave 的 `server-id` (e.g., 17/18)，并启用 GTID。
    - 建立主从复制后，在从库的 `my.cnf` 中加入 `read_only = 1`。

#### 4.2 MHA + Keepalived + 云平台VIP

- **MHA**:
    - **Manager**: 主`.10`，备`.9`。**均在宿主机上原生安装**，不容器化。
    - **Node**: `.7`, `.8`。
    - **核心配置**: 配置好免密SSH，并在 `app1.cnf` 中定义 `master_ip_failover_script`。该脚本的核心逻辑是通过 SSH **重启旧主库上的 Keepalived** 来触发VIP漂移。
    - **运维要点**: 执行任何计划内切换 (`masterha_master_switch`) 前，必须先执行 `masterha_stop`。
- **Keepalived**:
    - 由服务商部署在 `.7`, `.8` 上。
    - **核心配置**: 使用 `unicast` 模式，并在高优先级节点上设置 `state BACKUP` 以实现真正的 `nopreempt` (不抢占)。
    - **联动机制**: Keepalived 本身不监控 MySQL，其状态由 MHA 的钩子脚本通过 `systemctl restart` 命令进行驱动。

## 5. 中间件与日志平台部署

#### 5.1 ZooKeeper, RabbitMQ, Redis

- **目标节点**: `.4`, `.5`, `.6`
- **部署方式**: **在宿主机上原生安装**。
- **部署要点**:
    - **ZooKeeper**: 核心是配置好 `zoo.cfg` 集群列表和每个节点唯一的 `myid` 文件。
    - **RabbitMQ**: 核心是同步**完全一致**的 Erlang Cookie 文件。
    - **Redis**: 采用一主两从三哨兵模式。

#### 5.2 ClickHouse

- **目标节点**: `.9`
- **部署方式**: **在宿主机上原生安装**。
- **配置要点**: 不直接修改 `config.xml`，而是在 `/etc/clickhouse-server/conf.d/` 和 `users.d/` 目录下创建自定义配置文件来覆盖默认设置。

#### 5.3 Loki, Grafana, Prometheus

- **部署方式**: **全部作为 Swarm 服务**，通过独立的 `ops-stack.yml` 文件进行部署。
- **部署约束**: 通过 `placement: { constraints: [node.labels.ops == true] }`，将所有服务固定部署在 `.10` 节点上。
- **Promtail**: 在**所有10台服务器**上，使用 RPM 包原生安装。
    - **关键权限**: 必须将 `promtail` 用户加入 `docker` 组 (`sudo usermod -aG docker promtail`)。
    - **核心配置**: 使用 `docker_sd_configs` 自动发现容器，并通过 `pipeline_stages` 和 `relabel_configs` 提取标签。
- **Prometheus**:
    - **服务发现**: 使用 `dockerswarm_sd_configs`，通过挂载的 `docker.sock`，实现对 Swarm 内部应用的原生、实时服务发现。
    - **权限**: `prometheus` 服务需要在 `docker-stack.yml` 中配置 `group_add`，以获得访问 `docker.sock` 的权限。

## 6. 应用部署

- **部署方式**: 使用独立的 `app-stack.yml` 文件进行部署。
- **部署约束**: 通过 `placement: { constraints: [app == true] }`，将所有服务固定部署在 `.11` `.14` `.15` 节点上。
- **核心流程**:
    1. **镜像构建**: 后端使用 Buildpacks，前端使用多阶段构建。
    2. **镜像推送**: 推送到 `.10` 上的私有仓库。
    3. **服务编排 (`app-stack.yml`)**:
        - **网络**: 所有服务都加入 `app-net`。需要被监控的服务额外加入 `monitoring-net`。两个网络都声明为 `external: true`。
        - **主机名注入**: 使用 YAML 锚点统一管理 `extra_hosts`。
        - **日志策略**: **不使用 `volumes`**。所有应用日志直接输出到控制台（标准输出），由 Promtail 的 Docker 服务发现来采集。
        - **健康检查**: **不使用 Swarm 的 `healthcheck`**。依赖于 Prometheus 告警和业务层面的监控。
    4. **发布与更新**:
        - **始终使用 `docker stack deploy ...`** 的方式进行**滚动更新**，以规避 Swarm 底层的网络 Bug。

## 7. 端口开放申请列表 (面向云桌面运维)

内网节点之间默认全通，以下列表是需要为**云桌面环境**（运维、管理、监控人员）向服务商申请开放访问权限的端口。

| **用途 / 服务名称**        | **访问目标节点**                 | **目标端口** | **协议** | **说明**                        |
| -------------------------- | -------------------------------- | ------------ | -------- | ------------------------------- |
| **前端应用 (Zeus)**        | `************` (或任一Swarm节点) | `8013`       | TCP      | 访问前端管理界面 (后台系统)     |
| **API 网关 (Gateway API)** | `************` (或任一Swarm节点) | `8007`       | TCP      | API 对外访问入口                |
| **RabbitMQ 管理**          | `***********` (或任一中间件节点) | `15672`      | TCP      | 访问 RabbitMQ 管理后台          |
| **Grafana**                | `************` (ops-node-01)     | `3000`       | TCP      | 访问 Grafana 查看日志和监控图表 |
| **ElasticJob Console**     | `************` (或任一Swarm节点) | `8899`       | TCP      | 访问 ElasticJob 任务调度控制台  |
| **Prometheus UI**          | `************` (ops-node-01)     | `9090`       | TCP      | 访问 Prometheus 监控后台        |