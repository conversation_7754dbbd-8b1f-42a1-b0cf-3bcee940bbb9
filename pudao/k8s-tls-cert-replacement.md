# Kubernetes TLS证书更换指南

## 证书更换流程概览

基于你的历史记录，证书更换主要包含以下步骤：

### 1. 证书检查和准备阶段

```bash
# 检查当前证书状态
kubectl get ingress -A
kubectl get ingress <ingress-name> -n <namespace> -oyaml

# 检查现有证书信息
kubectl get secret <tls-secret-name> -n <namespace> -oyaml

# 验证证书有效期
kubectl get secret <tls-secret-name> -o jsonpath="{.data.tls\.crt}" -n <namespace> | base64 -d | openssl x509 -noout -text | grep "Not After"
```

### 2. 证书文件准备

确保你有以下文件：
- `domain.crt` 或 `domain_bundle.crt` (证书文件)
- `domain.key` (私钥文件)

### 3. 更换证书步骤

#### 方法一：删除并重新创建 Secret

```bash
# 删除旧的证书 Secret
kubectl delete secret <tls-secret-name> -n <namespace>

# 创建新的证书 Secret
kubectl create secret tls <tls-secret-name> -n <namespace> \
  --cert=<cert-file> \
  --key=<key-file>
```

#### 方法二：直接更新 Secret（推荐）

```bash
# 更新现有 Secret
kubectl create secret tls <tls-secret-name> -n <namespace> \
  --cert=<cert-file> \
  --key=<key-file> \
  --dry-run=client -o yaml | kubectl apply -f -
```

### 4. 验证更新

```bash
# 验证证书是否更新成功
kubectl get secret <tls-secret-name> -o jsonpath="{.data.tls\.crt}" -n <namespace> | base64 -d | openssl x509 -noout -text | grep "Not After"

# 检查 Ingress 配置
kubectl get ingress <ingress-name> -n <namespace> -oyaml
```

## 从你的记录中提取的实际案例

### 案例1: qa-01 环境证书更换

```bash
# 检查证书
kubectl get ingress jingway-api -n qa-01 -oyaml
kubectl get secret tls-ingress-nginx-unirap-qa-01 -n qa-01 -oyaml

# 验证当前证书
kubectl get secret tls-ingress-nginx-unirap-qa-01 -o jsonpath="{.data.tls\.crt}" -n qa-01 | base64 -d | openssl x509 -noout -text

# 更换证书
kubectl delete secret tls-ingress-nginx-unirap-qa-01 -n qa-01
kubectl create secret tls tls-ingress-nginx-unirap-qa-01 -n qa-01 \
  --cert=unirap.cn_bundle.crt \
  --key=unirap.cn.key

# 验证新证书
kubectl get secret tls-ingress-nginx-unirap-qa-01 -n qa-01 -o jsonpath="{.data.tls\.crt}" | base64 -d | openssl x509 -noout -text | grep "Not After"
```

### 案例2: production 环境证书更换

```bash
# 更换 tls-open-unirap 证书
kubectl delete secret tls-open-unirap -n production
kubectl create secret tls tls-open-unirap -n production \
  --cert=unirap.cn_bundle.crt \
  --key=unirap.cn.key

# 更换 tls-unirap-cn 证书
kubectl delete secret tls-unirap-cn -n production
kubectl create secret tls tls-unirap-cn -n production \
  --cert=unirap.cn_bundle.crt \
  --key=unirap.cn.key
```

## 通用证书更换脚本模板

```bash
#!/bin/bash

# 配置变量
NAMESPACE="your-namespace"
SECRET_NAME="your-tls-secret"
CERT_FILE="your-domain.crt"
KEY_FILE="your-domain.key"
INGRESS_NAME="your-ingress"

echo "开始更换证书: $SECRET_NAME 在命名空间: $NAMESPACE"

# 1. 检查当前证书
echo "检查当前证书状态..."
kubectl get secret $SECRET_NAME -n $NAMESPACE -o jsonpath="{.data.tls\.crt}" | base64 -d | openssl x509 -noout -dates

# 2. 备份当前证书（可选）
echo "备份当前证书..."
kubectl get secret $SECRET_NAME -n $NAMESPACE -oyaml > ${SECRET_NAME}-backup-$(date +%Y%m%d).yaml

# 3. 更新证书
echo "更新证书..."
kubectl delete secret $SECRET_NAME -n $NAMESPACE
kubectl create secret tls $SECRET_NAME -n $NAMESPACE \
  --cert=$CERT_FILE \
  --key=$KEY_FILE

# 4. 验证新证书
echo "验证新证书..."
kubectl get secret $SECRET_NAME -n $NAMESPACE -o jsonpath="{.data.tls\.crt}" | base64 -d | openssl x509 -noout -dates

# 5. 重启相关 Pod（如果需要）
echo "重启 Ingress Controller（如果需要）..."
# kubectl rollout restart deployment/ingress-nginx-controller -n ingress-nginx

echo "证书更换完成！"
```

## 注意事项

1. **备份原证书**: 在删除前务必备份原有证书配置
2. **验证证书**: 更换前后都要验证证书的有效性和到期时间
3. **测试连接**: 证书更换后测试相关服务的 HTTPS 连接
4. **多环境同步**: 如果有多个环境，确保所有环境的证书都更新
5. **监控告警**: 设置证书过期监控，避免证书过期导致的服务中断

## 批量更换脚本

如果需要批量更换多个证书，可以使用以下模板：

```bash
#!/bin/bash

# 定义证书映射关系
declare -A CERT_MAP=(
    ["qa-01|tls-ingress-nginx-unirap-qa-01"]="unirap.cn_bundle.crt|unirap.cn.key"
    ["production|tls-open-unirap"]="unirap.cn_bundle.crt|unirap.cn.key"
    ["production|tls-unirap-cn"]="unirap.cn_bundle.crt|unirap.cn.key"
)

for key in "${!CERT_MAP[@]}"; do
    IFS='|' read -r namespace secret_name <<< "$key"
    IFS='|' read -r cert_file key_file <<< "${CERT_MAP[$key]}"
    
    echo "更换证书: $secret_name 在命名空间: $namespace"
    
    kubectl delete secret $secret_name -n $namespace
    kubectl create secret tls $secret_name -n $namespace \
      --cert=$cert_file \
      --key=$key_file
      
    echo "验证证书: $secret_name"
    kubectl get secret $secret_name -n $namespace -o jsonpath="{.data.tls\.crt}" | base64 -d | openssl x509 -noout -dates
    echo "---"
done
```

这个指南应该能帮助你标准化 Kubernetes 中 TLS 证书的更换流程。