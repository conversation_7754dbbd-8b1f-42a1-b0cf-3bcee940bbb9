{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "limit": 100, "name": "Annotations & Alerts", "showIn": 0, "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "enable": true, "expr": "resets(process_uptime_seconds{app=\"$app\", instance=\"$instance\"}[2m]) > 0", "iconColor": "rgba(255, 96, 96, 1)", "limit": 100, "name": "进程重启打标", "showIn": 0, "step": "1m", "tagKeys": "restart-tag", "tags": [], "textFormat": "uptime reset", "titleFormat": "<PERSON><PERSON>", "type": "tags"}]}, "description": "Dashboard for Micrometer instrumented apps (Java, Spring Boot, Micronaut)", "editable": true, "fiscalYearStartMonth": 0, "gnetId": 12856, "graphTooltip": 1, "id": 13, "links": [], "liveNow": false, "panels": [{"collapsed": false, "datasource": {"uid": "${DS_SPRING-DEMOT}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 125, "panels": [], "targets": [{"datasource": {"uid": "${DS_SPRING-DEMOT}"}, "refId": "A"}], "title": "概览", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 1, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(245, 54, 54, 0.9)", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 0}, {"color": "#73BF69", "value": 10}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 0, "y": 1}, "id": 63, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "process_uptime_seconds{app=\"$app\", instance=\"$instance\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "metric": "", "range": true, "refId": "A", "step": 14400}], "title": "启动时长", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(245, 54, 54, 0.9)", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 0}, {"color": "rgba(50, 172, 45, 0.97)", "value": 1}]}, "unit": "dateTimeAsIso"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 6, "y": 1}, "id": 92, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "process_start_time_seconds{app=\"$app\", instance=\"$instance\"}*1000", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "metric": "", "range": true, "refId": "A", "step": 14400}], "title": "启动时间", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 2, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 70}, {"color": "rgba(245, 54, 54, 0.9)", "value": 90}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 12, "y": 1}, "id": 65, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "sum(jvm_memory_used_bytes{app=\"$app\", instance=\"$instance\", area=\"heap\"})*100/sum(jvm_memory_max_bytes{app=\"$app\",instance=\"$instance\", area=\"heap\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "range": true, "refId": "A", "step": 14400}], "title": "堆内存使用率", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 2, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}, {"options": {"from": -1e+32, "result": {"text": "N/A"}, "to": 0}, "type": "range"}], "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 70}, {"color": "rgba(245, 54, 54, 0.9)", "value": 90}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 18, "y": 1}, "id": 75, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "sum(jvm_memory_used_bytes{app=\"$app\", instance=\"$instance\", area=\"nonheap\"})*100/sum(jvm_memory_max_bytes{app=\"$app\",instance=\"$instance\", area=\"nonheap\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "range": true, "refId": "A", "step": 14400}], "title": "非堆内存使用率", "type": "stat"}, {"collapsed": true, "datasource": {"uid": "${DS_SPRING-DEMOT}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 4}, "id": 169, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 5}, "hiddenSeries": false, "id": 175, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "sum(rate(facade_seconds_count{app=\"$app\", instance=\"$instance\"}[2m])) by (func)", "legendFormat": "__auto", "range": true, "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "三方接口QPS(1分钟平均)", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "ops", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 5}, "hiddenSeries": false, "id": 177, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "(sum(irate(facade_seconds_sum{app=\"$app\", instance=\"$instance\"}[2m])) by (func)) / (sum(irate(facade_seconds_count{app=\"$app\", instance=\"$instance\"}[2m])) by (func))", "legendFormat": "__auto", "range": true, "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "三方接口响应时间(1分钟平均)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "s", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 5}, "hiddenSeries": false, "id": 218, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "sum(rate(facade_seconds_count{app=\"$app\", instance=\"$instance\", exception != \"none\"}[2m])) by (func) * 60", "legendFormat": "__auto", "range": true, "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "三方接口异常QPM", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "ops", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 13}, "hiddenSeries": false, "id": 313, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "sum(rate(exception_count_total{app=\"$app\", instance=\"$instance\", exception != \"none\"}[2m])) by (func) * 60", "legendFormat": "__auto", "range": true, "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "业务异常QPM", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "ops", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 13}, "hiddenSeries": false, "id": 284, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "sum(rate(exception_count_total{app=\"$app\", instance=\"$instance\", exception != \"none\"}[2m])) by (func) * 60", "legendFormat": "__auto", "range": true, "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "捕获异常QPM", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "ops", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 13}, "hiddenSeries": false, "id": 255, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "sum(rate(method_timed_seconds_count{app=\"$app\", instance=\"$instance\", exception != \"none\"}[2m])) by (func) * 60", "legendFormat": "__auto", "range": true, "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "服务异常QPM", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "ops", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "targets": [{"datasource": {"uid": "${DS_SPRING-DEMOT}"}, "refId": "A"}], "title": "APP", "type": "row"}, {"collapsed": true, "datasource": {"uid": "${DS_SPRING-DEMOT}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 5}, "id": 170, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 6}, "hiddenSeries": false, "id": 183, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "executor_pool_size_threads{app=\"$app\", instance=\"$instance\", name=\"$thread_pool\"}", "hide": false, "instant": false, "interval": "", "legendFormat": "线程池总数", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "executor_active_threads{app=\"$app\", instance=\"$instance\", name=\"$thread_pool\"}", "hide": false, "interval": "", "legendFormat": "线程池活跃数量", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "rate(executor_completed_tasks_total{app=\"$app\", instance=\"$instance\", name=\"$thread_pool\"}[2m]) * 60", "interval": "", "legendFormat": "任务完成量 / 分钟", "refId": "C"}], "thresholds": [], "timeRegions": [], "title": "线程池", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 6}, "hiddenSeries": false, "id": 188, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "executor_queued_tasks{app=\"$app\", instance=\"$instance\", name=\"$thread_pool\"}", "hide": false, "instant": false, "interval": "", "legendFormat": "待处理任务数", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "executor_queue_remaining_tasks{app=\"$app\", instance=\"$instance\", name=\"$thread_pool\"}", "hide": false, "interval": "", "legendFormat": "可入队任务数", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "线程池队列", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 6}, "hiddenSeries": false, "id": 189, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "sum(rate(executor_seconds_sum{app=\"$app\", instance=\"$instance\", name=\"$thread_pool\"}[2m])) / sum(rate(executor_seconds_count{app=\"$app\", instance=\"$instance\", name=\"$thread_pool\"}[2m]))", "hide": false, "instant": false, "interval": "", "legendFormat": "执行时间(1分钟平均)", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "sum(rate(executor_idle_seconds_sum{app=\"$app\", instance=\"$instance\", name=\"$thread_pool\"}[2m])) / sum(rate(executor_idle_seconds_count{app=\"$app\", instance=\"$instance\", name=\"$thread_pool\"}[2m]))", "hide": false, "interval": "", "legendFormat": "空闲时间(1分钟平均)", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "线程池时间消耗", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "ms", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "repeat": "thread_pool", "targets": [{"datasource": {"uid": "${DS_SPRING-DEMOT}"}, "refId": "A"}], "title": "线程池 - $thread_pool", "type": "row"}, {"collapsed": true, "datasource": {"type": "datasource", "uid": "grafana"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 6}, "id": 145, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 7}, "hiddenSeries": false, "id": 151, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "hikaricp_connections{app=\"$app\", instance=\"$instance\", pool=\"$hikaricp_pool\"}", "hide": false, "interval": "", "legendFormat": "Total", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "hikaricp_connections_active{app=\"$app\", instance=\"$instance\", pool=\"$hikaricp_pool\"}", "interval": "", "legendFormat": "Active", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "hikaricp_connections_idle{app=\"$app\", instance=\"$instance\", pool=\"$hikaricp_pool\"}", "interval": "", "legendFormat": "Idle", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "hikaricp_connections_pending{app=\"$app\", instance=\"$instance\", pool=\"$hikaricp_pool\"}", "interval": "", "legendFormat": "Pending", "refId": "D"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "irate(hikaricp_connections_timeout_total{app=\"$app\", instance=\"$instance\", pool=\"$hikaricp_pool\"}[2m])", "hide": false, "interval": "", "legendFormat": "Timeout", "refId": "E"}], "thresholds": [], "timeRegions": [], "title": "连接池大小", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 7}, "hiddenSeries": false, "id": 157, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "irate(hikaricp_connections_usage_seconds_sum{app=\"$app\", instance=\"$instance\", pool=\"$hikaricp_pool\"}[2m]) / irate(hikaricp_connections_usage_seconds_count{app=\"$app\", instance=\"$instance\", pool=\"$hikaricp_pool\"}[2m])", "interval": "", "legendFormat": "Usage seconds", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "irate(hikaricp_connections_acquire_seconds_sum{app=\"$app\", instance=\"$instance\", pool=\"$hikaricp_pool\"}[2m]) / irate(hikaricp_connections_acquire_seconds_count{app=\"$app\", instance=\"$instance\", pool=\"$hikaricp_pool\"}[2m])", "hide": false, "legendFormat": "Acquire seconds", "range": true, "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "时间消耗", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "s", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 7}, "hiddenSeries": false, "id": 163, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "irate(hikaricp_connections_creation_seconds_sum{app=\"$app\", instance=\"$instance\", pool=\"$hikaricp_pool\"}[2m]) / irate(hikaricp_connections_creation_seconds_count{app=\"$app\", instance=\"$instance\", pool=\"$hikaricp_pool\"}[2m])", "legendFormat": "Creation seconds", "range": true, "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "创建耗时", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "s", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "repeat": "hikaricp_pool", "repeatDirection": "h", "targets": [{"datasource": {"type": "datasource", "uid": "grafana"}, "refId": "A"}], "title": "Hikaricp 统计 - $hikaricp_pool", "type": "row"}, {"collapsed": true, "datasource": {"uid": "${DS_SPRING-DEMOT}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 8}, "id": 126, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "description": "平均每秒处理的请求数", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 9}, "hiddenSeries": false, "id": 111, "legend": {"avg": false, "current": true, "max": false, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "sum(irate(http_server_requests_seconds_count{app=\"$app\", instance=\"$instance\"}[2m]))", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "HTTP", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "总QPS(1分钟平均)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "ops", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {"HTTP": "#890f02", "HTTP - 5xx": "#bf1b00"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 6, "y": 9}, "hiddenSeries": false, "id": 112, "legend": {"avg": false, "current": true, "max": false, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "sum(rate(http_server_requests_seconds_count{app=\"$app\", instance=\"$instance\", status=~\"5..\"}[2m]))", "format": "time_series", "intervalFactor": 1, "legendFormat": "HTTP - 5xx", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "总错误数(1分钟平均)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "ops", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 12, "y": 9}, "hiddenSeries": false, "id": 113, "legend": {"avg": false, "current": true, "max": false, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "sum(rate(http_server_requests_seconds_sum{app=\"$app\", instance=\"$instance\", status!~\"5..\"}[2m]))/sum(rate(http_server_requests_seconds_count{app=\"$app\", instance=\"$instance\", status!~\"5..\"}[2m]))", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "HTTP - AVG", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "max(http_server_requests_seconds_max{app=\"$app\", instance=\"$instance\", status!~\"5..\"})", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "HTTP - MAX", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "总请求耗时(1分钟平均)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "s", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "description": "饱和度指标来自于Google SRE的的黄金指标, 指服务的过载程度, 当系统过载时, 往往意味着请求需要排队处理", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 9}, "hiddenSeries": false, "id": 119, "legend": {"alignAsTable": false, "avg": false, "current": true, "max": false, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "tomcat_threads_busy_threads{app=\"$app\", instance=\"$instance\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "TOMCAT - BSY", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "tomcat_threads_current_threads{app=\"$app\", instance=\"$instance\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "TOMCAT - CUR", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "tomcat_threads_config_max_threads{app=\"$app\", instance=\"$instance\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "TOMCAT - MAX", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "jetty_threads_busy{app=\"$app\", instance=\"$instance\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "JETTY - BSY", "refId": "D"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "jetty_threads_current{app=\"$app\", instance=\"$instance\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "JETTY - CUR", "refId": "E"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "jetty_threads_config_max{app=\"$app\", instance=\"$instance\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "JETTY - MAX", "refId": "F"}], "thresholds": [], "timeRegions": [], "title": "饱和度", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "description": "单接口平均每秒处理的请求数", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 16}, "hiddenSeries": false, "id": 223, "legend": {"avg": false, "current": true, "max": false, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "sum(irate(http_server_requests_seconds_count{app=\"$app\", instance=\"$instance\"}[2m])) by (uri)", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{uri}}", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "单接口QPS(1分钟平均)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "ops", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {"HTTP": "#890f02", "HTTP - 5xx": "#bf1b00"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 6, "y": 16}, "hiddenSeries": false, "id": 226, "legend": {"avg": false, "current": true, "max": false, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "sum(rate(http_server_requests_seconds_count{app=\"$app\", instance=\"$instance\", status=~\"5..\"}[2m])) by (uri)", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{uri}}", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "单接口错误数(1分钟平均)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "ops", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 12, "y": 16}, "hiddenSeries": false, "id": 224, "legend": {"avg": false, "current": true, "max": false, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "sum(rate(http_server_requests_seconds_sum{app=\"$app\", instance=\"$instance\", status!~\"5..\"}[2m])) by (uri)/sum(rate(http_server_requests_seconds_count{app=\"$app\", instance=\"$instance\", status!~\"5..\"}[2m])) by (uri)", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{uri}}", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "单接口请求平均耗时(1分钟平均)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "s", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 16}, "hiddenSeries": false, "id": 225, "legend": {"avg": false, "current": true, "max": false, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "max(http_server_requests_seconds_max{app=\"$app\", instance=\"$instance\", status!~\"5..\"}) by (uri)", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{uri}}", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "单接口请求最大耗时(1分钟平均)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "s", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "targets": [{"datasource": {"uid": "${DS_SPRING-DEMOT}"}, "refId": "A"}], "title": "服务黄金指标", "type": "row"}, {"collapsed": false, "datasource": {"uid": "${DS_SPRING-DEMOT}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 9}, "id": 127, "panels": [], "targets": [{"datasource": {"uid": "${DS_SPRING-DEMOT}"}, "refId": "A"}], "title": "JVM 内存", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 10}, "hiddenSeries": false, "id": 26, "legend": {"alignAsTable": false, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "sum(jvm_memory_used_bytes{app=\"$app\", instance=\"$instance\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "used", "metric": "", "range": true, "refId": "A", "step": 2400}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "sum(jvm_memory_committed_bytes{app=\"$app\", instance=\"$instance\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "committed", "refId": "B", "step": 2400}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "sum(jvm_memory_max_bytes{app=\"$app\", instance=\"$instance\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "max", "refId": "C", "step": 2400}], "thresholds": [], "timeRegions": [], "title": "总内存", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["mbytes", "short"], "yaxes": [{"format": "bytes", "label": "", "logBase": 1, "min": 0, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 6, "x": 6, "y": 10}, "hiddenSeries": false, "id": 24, "legend": {"avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "sum(jvm_memory_used_bytes{app=\"$app\", instance=\"$instance\", area=\"heap\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "used", "metric": "", "refId": "A", "step": 2400}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "sum(jvm_memory_committed_bytes{app=\"$app\", instance=\"$instance\", area=\"heap\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "committed", "refId": "B", "step": 2400}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "sum(jvm_memory_max_bytes{app=\"$app\", instance=\"$instance\", area=\"heap\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "max", "refId": "C", "step": 2400}], "thresholds": [], "timeRegions": [], "title": "堆内存", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["mbytes", "short"], "yaxes": [{"format": "bytes", "logBase": 1, "min": 0, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 6, "x": 12, "y": 10}, "hiddenSeries": false, "id": 25, "legend": {"avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "sum(jvm_memory_used_bytes{app=\"$app\", instance=\"$instance\", area=\"nonheap\"})", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "used", "metric": "", "refId": "A", "step": 2400}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "sum(jvm_memory_committed_bytes{app=\"$app\", instance=\"$instance\", area=\"nonheap\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "committed", "refId": "B", "step": 2400}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "sum(jvm_memory_max_bytes{app=\"$app\", instance=\"$instance\", area=\"nonheap\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "max", "refId": "C", "step": 2400}], "thresholds": [], "timeRegions": [], "title": "非堆内存", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["mbytes", "short"], "yaxes": [{"format": "bytes", "logBase": 1, "min": 0, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "description": "从操作系统层面看JVM进程的内存使用, 因为JVM并不是直接按照配置的内存参数申请全部内存", "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 10}, "hiddenSeries": false, "id": 86, "legend": {"avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "process_memory_vss_bytes{app=\"$app\", instance=\"$instance\"}", "format": "time_series", "hide": true, "intervalFactor": 2, "legendFormat": "vss", "metric": "", "refId": "A", "step": 2400}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "process_memory_rss_bytes{app=\"$app\", instance=\"$instance\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "rss", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "process_memory_swap_bytes{app=\"$app\", instance=\"$instance\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "swap", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "process_memory_rss_bytes{app=\"$app\", instance=\"$instance\"} + process_memory_swap_bytes{app=\"$app\", instance=\"$instance\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "total", "refId": "D"}], "thresholds": [], "timeRegions": [], "title": "JVM 进程内存", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["mbytes", "short"], "yaxes": [{"format": "bytes", "label": "", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 17}, "id": 129, "panels": [], "repeat": "persistence_counts", "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "refId": "A"}], "title": "JVM 堆内存详细", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 18}, "hiddenSeries": false, "id": 3, "legend": {"alignAsTable": false, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxPerRow": 3, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 5, "points": false, "renderer": "flot", "repeat": "jvm_memory_pool_heap", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "jvm_memory_used_bytes{app=\"$app\", instance=\"$instance\", id=~\"$jvm_memory_pool_heap\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "used", "metric": "", "refId": "A", "step": 1800}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "jvm_memory_committed_bytes{app=\"$app\", instance=\"$instance\", id=~\"$jvm_memory_pool_heap\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "commited", "metric": "", "refId": "B", "step": 1800}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "jvm_memory_max_bytes{app=\"$app\", instance=\"$instance\", id=~\"$jvm_memory_pool_heap\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "max", "metric": "", "refId": "C", "step": 1800}], "thresholds": [], "timeRegions": [], "title": "$jvm_memory_pool_heap", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["mbytes", "short"], "yaxes": [{"format": "bytes", "logBase": 1, "min": 0, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 25}, "id": 130, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "refId": "A"}], "title": "JVM 非堆内存详细", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 26}, "hiddenSeries": false, "id": 78, "legend": {"alignAsTable": false, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxPerRow": 3, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 5, "points": false, "renderer": "flot", "repeat": "jvm_memory_pool_nonheap", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "jvm_memory_used_bytes{app=\"$app\", instance=\"$instance\", id=~\"$jvm_memory_pool_nonheap\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "used", "metric": "", "refId": "A", "step": 1800}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "jvm_memory_committed_bytes{app=\"$app\", instance=\"$instance\", id=~\"$jvm_memory_pool_nonheap\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "commited", "metric": "", "refId": "B", "step": 1800}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "jvm_memory_max_bytes{app=\"$app\", instance=\"$instance\", id=~\"$jvm_memory_pool_nonheap\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "max", "metric": "", "refId": "C", "step": 1800}], "thresholds": [], "timeRegions": [], "title": "$jvm_memory_pool_nonheap", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["mbytes", "short"], "yaxes": [{"format": "bytes", "logBase": 1, "min": 0, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 33}, "id": 131, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "refId": "A"}], "title": "垃圾回收(GC)", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "description": "", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 34}, "hiddenSeries": false, "id": 98, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "rate(jvm_gc_pause_seconds_count{app=\"$app\", instance=\"$instance\"}[2m])", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "{{action}} ({{cause}})", "range": true, "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "GC 次数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "ops", "logBase": 1, "min": "0", "show": true}, {"format": "short", "label": "", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 34}, "hiddenSeries": false, "id": 101, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "rate(jvm_gc_pause_seconds_sum{app=\"$app\", instance=\"$instance\"}[2m])/rate(jvm_gc_pause_seconds_count{app=\"$app\", instance=\"$instance\"}[2m])", "format": "time_series", "hide": false, "instant": false, "intervalFactor": 1, "legendFormat": "avg {{action}} ({{cause}})", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "jvm_gc_pause_seconds_max{app=\"$app\", instance=\"$instance\"}", "format": "time_series", "hide": false, "instant": false, "intervalFactor": 1, "legendFormat": "max {{action}} ({{cause}})", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "GC暂停时间", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "s", "logBase": 1, "min": "0", "show": true}, {"format": "short", "label": "", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "description": "内存分配的大小, 以及从新生代晋升到老年代的内存大小", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 34}, "hiddenSeries": false, "id": 99, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "rate(jvm_gc_memory_allocated_bytes_total{app=\"$app\", instance=\"$instance\"}[2m])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "allocated", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "rate(jvm_gc_memory_promoted_bytes_total{app=\"$app\", instance=\"$instance\"}[2m])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "promoted", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "内存分配/晋升", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "Bps", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 41}, "id": 128, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "refId": "A"}], "title": "JVM 负载", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 42}, "hiddenSeries": false, "id": 106, "legend": {"avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "system_cpu_usage{app=\"$app\", instance=\"$instance\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "system", "metric": "", "refId": "A", "step": 2400}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "process_cpu_usage{app=\"$app\", instance=\"$instance\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "process", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "avg_over_time(process_cpu_usage{app=\"$app\", instance=\"$instance\"}[1h])", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "process-1h", "refId": "C"}], "thresholds": [], "timeRegions": [], "title": "CPU 使用率", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["short", "short"], "yaxes": [{"decimals": 1, "format": "percentunit", "label": "", "logBase": 1, "max": "1", "min": 0, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 6, "x": 6, "y": 42}, "hiddenSeries": false, "id": 93, "legend": {"avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "system_load_average_1m{app=\"$app\", instance=\"$instance\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "load1", "metric": "", "refId": "A", "step": 2400}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "system_cpu_count{app=\"$app\", instance=\"$instance\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "cpu核数", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "近1分钟负载", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["short", "short"], "yaxes": [{"decimals": 1, "format": "short", "label": "", "logBase": 1, "min": 0, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 6, "x": 12, "y": 42}, "hiddenSeries": false, "id": 32, "legend": {"avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "jvm_threads_live_threads{app=\"$app\", instance=\"$instance\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "live", "metric": "", "refId": "A", "step": 2400}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "jvm_threads_daemon_threads{app=\"$app\", instance=\"$instance\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "daemon", "metric": "", "refId": "B", "step": 2400}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "jvm_threads_peak_threads{app=\"$app\", instance=\"$instance\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "peak", "refId": "C", "step": 2400}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "process_threads{app=\"$app\", instance=\"$instance\"}", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "process", "refId": "D", "step": 2400}], "thresholds": [], "timeRegions": [], "title": "线程数", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["short", "short"], "yaxes": [{"decimals": 0, "format": "short", "logBase": 1, "min": 0, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {"blocked": "#bf1b00", "new": "#fce2de", "runnable": "#7eb26d", "terminated": "#511749", "timed-waiting": "#c15c17", "waiting": "#eab839"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "description": "需特别关注blocked的线程数, 这意味着线程被阻塞了, 如果线程全部是blocked状态, 则系统无法处理新请求", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 42}, "hiddenSeries": false, "id": 124, "legend": {"alignAsTable": false, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "jvm_threads_states_threads{app=\"$app\", instance=\"$instance\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{state}}", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "各状态线程数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {"debug": "#1F78C1", "error": "#BF1B00", "info": "#508642", "trace": "#6ED0E0", "warn": "#EAB839"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 18, "x": 0, "y": 49}, "height": "", "hiddenSeries": false, "id": 91, "legend": {"alignAsTable": false, "avg": false, "current": true, "hideEmpty": false, "hideZero": false, "max": true, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": true, "pluginVersion": "9.3.6", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "error", "yaxis": 1}, {"alias": "warn", "yaxis": 1}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "increase(logback_events_total{app=\"$app\", instance=\"$instance\"}[2m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{level}}", "metric": "", "refId": "A", "step": 1200}], "thresholds": [], "timeRegions": [], "title": "Logback日志数", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["short", "short"], "yaxes": [{"decimals": 0, "format": "opm", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 49}, "hiddenSeries": false, "id": 61, "legend": {"avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "process_files_open_files{app=\"$app\", instance=\"$instance\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "open", "metric": "", "refId": "A", "step": 2400}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "process_files_max_files{app=\"$app\", instance=\"$instance\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "max", "metric": "", "refId": "B", "step": 2400}], "thresholds": [], "timeRegions": [], "title": "文件描述符", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["short", "short"], "yaxes": [{"decimals": 0, "format": "short", "logBase": 10, "min": 0, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 56}, "id": 132, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "refId": "A"}], "title": "类加载", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 57}, "hiddenSeries": false, "id": 37, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "jvm_classes_loaded_classes{app=\"$app\", instance=\"$instance\"}", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "Classes loaded", "metric": "", "refId": "A", "step": 1200}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "irate(jvm_classes_unloaded_classes_total{app=\"$app\", instance=\"$instance\"}[2m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "Classes  unloaded", "metric": "", "refId": "B", "step": 1200}], "thresholds": [], "timeRegions": [], "title": "已加载的类的数量", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["short", "short"], "yaxes": [{"format": "short", "logBase": 1, "min": 0, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "description": "可能增加或减少, 在Java中使用一些脚本语言例如groovy时, 需要关注, 防止因为逻辑异常产生大量的类, 进而导致metaspace满, 而metaspace满会触发full gc, 如无法释放则会导致JVM hang住", "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 57}, "hiddenSeries": false, "id": 38, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "delta(jvm_classes_loaded_classes{app=\"$app\",instance=\"$instance\"}[2m])", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "delta-1m", "metric": "", "refId": "A", "step": 1200}], "thresholds": [], "timeRegions": [], "title": "加载类数量变化", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["ops", "short"], "yaxes": [{"format": "short", "label": "", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 64}, "id": 133, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "refId": "A"}], "title": "Buffer Pools", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 65}, "hiddenSeries": false, "id": 33, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "jvm_buffer_memory_used_bytes{app=\"$app\", instance=\"$instance\", id=\"direct\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "used", "metric": "", "refId": "A", "step": 2400}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "jvm_buffer_total_capacity_bytes{app=\"$app\", instance=\"$instance\", id=\"direct\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "capacity", "metric": "", "refId": "B", "step": 2400}], "thresholds": [], "timeRegions": [], "title": "Direct Buffers", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["short", "short"], "yaxes": [{"format": "bytes", "logBase": 1, "min": 0, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 6, "x": 6, "y": 65}, "hiddenSeries": false, "id": 83, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "jvm_buffer_count_buffers{app=\"$app\", instance=\"$instance\", id=\"direct\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "count", "metric": "", "refId": "A", "step": 2400}], "thresholds": [], "timeRegions": [], "title": "Direct Buffers", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["short", "short"], "yaxes": [{"decimals": 0, "format": "short", "logBase": 1, "min": 0, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 6, "x": 12, "y": 65}, "hiddenSeries": false, "id": 85, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "jvm_buffer_memory_used_bytes{app=\"$app\", instance=\"$instance\", id=\"mapped\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "used", "metric": "", "refId": "A", "step": 2400}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "jvm_buffer_total_capacity_bytes{app=\"$app\", instance=\"$instance\", id=\"mapped\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "capacity", "metric": "", "refId": "B", "step": 2400}], "thresholds": [], "timeRegions": [], "title": "Mapped Buffers", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["short", "short"], "yaxes": [{"format": "bytes", "logBase": 1, "min": 0, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 65}, "hiddenSeries": false, "id": 84, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "expr": "jvm_buffer_count_buffers{app=\"$app\", instance=\"$instance\", id=\"mapped\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "count", "metric": "", "refId": "A", "step": 2400}], "thresholds": [], "timeRegions": [], "title": "Mapped Buffers", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["short", "short"], "yaxes": [{"decimals": 0, "format": "short", "logBase": 1, "min": 0, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "refresh": false, "schemaVersion": 37, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": false, "text": "jingway-api", "value": "jingway-api"}, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "definition": "label_values(jvm_memory_used_bytes{}, app)", "hide": 0, "includeAll": false, "label": "App", "multi": false, "name": "app", "options": [], "query": {"query": "label_values(jvm_memory_used_bytes{}, app)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"allFormat": "glob", "current": {"selected": false, "text": "************:9090", "value": "************:9090"}, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "definition": "label_values(jvm_memory_used_bytes{app=\"$app\"}, instance)", "hide": 0, "includeAll": false, "label": "Instance", "multi": false, "multiFormat": "glob", "name": "instance", "options": [], "query": {"query": "label_values(jvm_memory_used_bytes{app=\"$app\"}, instance)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "definition": "label_values(executor_pool_size_threads{app=\"$app\", instance=\"$instance\"}, name)", "hide": 0, "includeAll": true, "label": "Thread Pool", "multi": true, "name": "thread_pool", "options": [], "query": {"query": "label_values(executor_pool_size_threads{app=\"$app\", instance=\"$instance\"}, name)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "definition": "label_values(hikaricp_connections_min{app=\"$app\", instance=\"$instance\"}, pool)", "hide": 0, "includeAll": true, "label": "Hikaricp  Pool", "multi": true, "name": "hikaricp_pool", "options": [], "query": {"query": "label_values(hikaricp_connections_min{app=\"$app\", instance=\"$instance\"}, pool)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"allFormat": "glob", "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "definition": "label_values(jvm_memory_used_bytes{app=\"$app\", instance=\"$instance\", area=\"heap\"},id)", "hide": 2, "includeAll": true, "label": "JVM Memory Pools Heap", "multi": false, "multiFormat": "glob", "name": "jvm_memory_pool_heap", "options": [], "query": {"query": "label_values(jvm_memory_used_bytes{app=\"$app\", instance=\"$instance\", area=\"heap\"},id)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"allFormat": "glob", "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "definition": "label_values(jvm_memory_used_bytes{app=\"$app\", instance=\"$instance\", area=\"nonheap\"},id)", "hide": 2, "includeAll": true, "label": "JVM Memory Pools Non-Heap", "multi": false, "multiFormat": "glob", "name": "jvm_memory_pool_nonheap", "options": [], "query": {"query": "label_values(jvm_memory_used_bytes{app=\"$app\", instance=\"$instance\", area=\"nonheap\"},id)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 2, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-12h", "to": "now"}, "timepicker": {"now": true, "refresh_intervals": ["10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "title": "JVM Dashboard", "uid": "UOJjh1SMz", "version": 8, "weekStart": ""}