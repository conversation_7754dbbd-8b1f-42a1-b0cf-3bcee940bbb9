{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 1, "id": 15, "links": [], "liveNow": false, "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 9, "panels": [], "title": "外部数据源", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 1}, "id": 2, "options": {"legend": {"calcs": ["min", "max", "mean", "last", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "(sum by (dataSource) (rate(ds_invoke_cost_seconds_sum{dataSource=~\"$dataSource\",reportType=~\"$reportType\"}[2m]))) / sum by (dataSource) (rate(ds_invoke_cost_seconds_count{dataSource=~\"$dataSource\",reportType=~\"$reportType\"}[2m]))", "legendFormat": "{{dataSource}}", "range": true, "refId": "A"}], "title": "外部数据源平均响应时间（按dataSource分）", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 1}, "id": 3, "options": {"legend": {"calcs": ["min", "max", "mean", "last", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "max by (dataSource) (ds_invoke_cost_seconds_max{dataSource=~\"$dataSource\",reportType=~\"$reportType\"})", "legendFormat": "{{dataSource}}", "range": true, "refId": "A"}], "title": "外部数据源最大响应时间（按dataSource分）", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 11}, "id": 6, "options": {"legend": {"calcs": ["min", "max", "mean", "last", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "(sum by (dataSource,reportType) (rate(ds_invoke_cost_seconds_sum{dataSource=~\"$dataSource\",reportType=~\"$reportType\"}[2m]))) / sum by (dataSource,reportType) (rate(ds_invoke_cost_seconds_count{dataSource=~\"$dataSource\",reportType=~\"$reportType\"}[2m]))", "legendFormat": "{{dataSource}} - {{reportType}}", "range": true, "refId": "A"}], "title": "外部数据源平均响应时间（按reportType分）", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 11}, "id": 7, "options": {"legend": {"calcs": ["min", "max", "mean", "last", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "max by (dataSource, reportType) (ds_invoke_cost_seconds_max{dataSource=~\"$dataSource\",reportType=~\"$reportType\"})", "legendFormat": "{{dataSource}} - {{reportType}}", "range": true, "refId": "A"}], "title": "外部数据源最大响应时间（按reportType分）", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 10, "w": 8, "x": 0, "y": 21}, "id": 14, "options": {"legend": {"calcs": ["min", "max", "mean", "last", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "sum (rate(ds_invoke_cost_seconds_count{}[2m]))", "legendFormat": "总QPS", "range": true, "refId": "A"}], "title": "2分钟平均QPS（总）", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 10, "w": 8, "x": 8, "y": 21}, "id": 15, "options": {"legend": {"calcs": ["min", "max", "mean", "last", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "sum by (dataSource) (rate(ds_invoke_cost_seconds_count{dataSource=~\"$dataSource\",reportType=~\"$reportType\"}[2m]))", "legendFormat": "{{dataSource}}", "range": true, "refId": "A"}], "title": "2分钟平均QPS（按dataSource分）", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 10, "w": 8, "x": 16, "y": 21}, "id": 16, "options": {"legend": {"calcs": ["min", "max", "mean", "last", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "sum by (dataSource,reportType) (rate(ds_invoke_cost_seconds_count{dataSource=~\"$dataSource\",reportType=~\"$reportType\"}[2m]))", "legendFormat": "{{dataSource}} - {{reportType}}", "range": true, "refId": "A"}], "title": "2分钟平均QPS（按reportType分）", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 31}, "id": 18, "options": {"displayLabels": ["name", "percent"], "legend": {"displayMode": "table", "placement": "right", "showLegend": true, "values": ["value", "percent"]}, "pieType": "donut", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "sum by (dataSource) (ds_invoke_cost_seconds_count{dataSource=~\"$dataSource\",reportType=~\"$reportType\"})", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "调用总量（按dataSource分）", "type": "piechart"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 31}, "id": 19, "options": {"displayLabels": ["name", "percent"], "legend": {"displayMode": "table", "placement": "right", "showLegend": true, "values": ["value", "percent"]}, "pieType": "donut", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "sum by (dataSource,reportType) (ds_invoke_cost_seconds_count{dataSource=~\"$dataSource\",reportType=~\"$reportType\"})", "legendFormat": "{{dataSource}} - {{reportType}}", "range": true, "refId": "A"}], "title": "调用总量（按reportType分）", "type": "piechart"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 31}, "id": 20, "options": {"legend": {"calcs": ["min", "max", "mean", "last", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "sum by (dataSource,reportType) (rate(ds_invoke_exception_total{dataSource=~\"$dataSource\",reportType=~\"$reportType\"}[2m]))", "legendFormat": "{{dataSource}} - {{reportType}}", "range": true, "refId": "A"}], "title": "异常报告拉取QPS", "type": "timeseries"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 39}, "id": 11, "panels": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 40}, "id": 4, "options": {"legend": {"calcs": ["min", "max", "mean", "last", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "(sum by (dataSource) (rate(tako_invoke_cost_seconds_sum{dataSource=~\"$dataSource\",reportType=~\"$reportType\"}[2m]))) / sum by (dataSource) (rate(tako_invoke_cost_seconds_count{dataSource=~\"$dataSource\",reportType=~\"$reportType\"}[2m]))", "legendFormat": "{{dataSource}}", "range": true, "refId": "A"}], "title": "平均响应时间（按dataSource分）", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 40}, "id": 5, "options": {"legend": {"calcs": ["min", "max", "mean", "last", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "max by (dataSource) (tako_invoke_cost_seconds_max{dataSource=~\"$dataSource\",reportType=~\"$reportType\"})", "legendFormat": "{{dataSource}}", "range": true, "refId": "A"}], "title": "最大响应时间（按dataSource分）", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 50}, "id": 12, "options": {"legend": {"calcs": ["min", "max", "mean", "last", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "(sum by (dataSource,reportType) (rate(tako_invoke_cost_seconds_sum{dataSource=~\"$dataSource\",reportType=~\"$reportType\"}[2m]))) / sum by (dataSource,reportType) (rate(tako_invoke_cost_seconds_count{dataSource=~\"$dataSource\",reportType=~\"$reportType\"}[2m]))", "legendFormat": "{{dataSource}} - {{reportType}}", "range": true, "refId": "A"}], "title": "平均响应时间（按reportType分）", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 50}, "id": 13, "options": {"legend": {"calcs": ["min", "max", "mean", "last", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "max by (dataSource,reportType) (tako_invoke_cost_seconds_max{dataSource=~\"$dataSource\",reportType=~\"$reportType\"})", "legendFormat": "{{dataSource}} - {{reportType}}", "range": true, "refId": "A"}], "title": "最大响应时间（按reportType分）", "type": "timeseries"}], "title": "总时间", "type": "row"}], "refresh": false, "schemaVersion": 37, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "definition": "label_values(ds_invoke_cost_seconds_max{app=\"tako-api\"}, dataSource)", "hide": 0, "includeAll": true, "multi": true, "name": "dataSource", "options": [], "query": {"query": "label_values(ds_invoke_cost_seconds_max{app=\"tako-api\"}, dataSource)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "type": "query"}, {"current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "definition": "label_values(ds_invoke_cost_seconds_max{app=\"tako-api\", dataSource=~\"$dataSource\"}, reportType)", "hide": 0, "includeAll": true, "multi": true, "name": "reportType", "options": [], "query": {"query": "label_values(ds_invoke_cost_seconds_max{app=\"tako-api\", dataSource=~\"$dataSource\"}, reportType)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "type": "query"}]}, "time": {"from": "now-2h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "tako-api", "uid": "LwEMgz-Vk", "version": 13, "weekStart": "monday"}