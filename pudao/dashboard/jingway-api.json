{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}, {"datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "iconColor": "red", "name": "customer", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 14, "links": [], "liveNow": false, "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 24, "panels": [], "title": "QPS", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 1}, "id": 13, "options": {"legend": {"calcs": ["min", "max", "mean", "last", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "9.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "sum (rate(jingway_api_apply_cost_time_seconds_count{finalCustomer=\"true\"}[2m]))", "legendFormat": "总QPS", "range": true, "refId": "A"}], "title": "2分钟平均QPS（总）", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 1}, "id": 14, "options": {"legend": {"calcs": ["min", "max", "mean", "last", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "9.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "sum by (customer) (rate(jingway_api_apply_cost_time_seconds_count{customer=~\"$customer\",productCode=~\"$productCode\"}[2m]))", "legendFormat": "{{customer}}", "range": true, "refId": "A"}], "title": "2分钟平均QPS（按customer分）", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 10}, "id": 16, "options": {"legend": {"calcs": ["min", "max", "mean", "last", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "9.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "sum by (customer, productCode) (rate(jingway_api_apply_cost_time_seconds_count{customer=~\"$customer\",productCode=~\"$productCode\"}[2m]))", "legendFormat": "{{customer}} - {{productCode}}", "range": true, "refId": "A"}], "title": "2分钟平均QPS（按customer和productCode分）", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 10}, "id": 15, "options": {"legend": {"calcs": ["min", "max", "mean", "last", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "9.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "sum by (productCode) (rate(jingway_api_apply_cost_time_seconds_count{customer=~\"$customer\",productCode=~\"$productCode\", finalCustomer=\"true\"}[2m]))", "legendFormat": "{{productCode}}", "range": true, "refId": "A"}], "title": "2分钟平均QPS（按productCode分）", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 19}, "id": 22, "panels": [], "title": "响应时间", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 10, "w": 8, "x": 0, "y": 20}, "id": 2, "options": {"legend": {"calcs": ["min", "max", "mean", "last", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "9.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "(sum by (productCode) (rate(jingway_api_apply_cost_time_seconds_sum{customer=~\"$customer\",productCode=~\"$productCode\"}[2m]))) / sum by (productCode) (rate(jingway_api_apply_cost_time_seconds_count{customer=~\"$customer\",productCode=~\"$productCode\"}[2m]))", "legendFormat": "{{productCode}}", "range": true, "refId": "A"}], "title": "平均响应时间(按productCode分)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 10, "w": 8, "x": 8, "y": 20}, "id": 4, "options": {"legend": {"calcs": ["min", "max", "mean", "last", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "max by (productCode) (jingway_api_apply_cost_time_seconds_max{customer=~\"$customer\",productCode=~\"$productCode\"})", "legendFormat": "{{productCode}}", "range": true, "refId": "A"}], "title": "最大耗时(按productCode分)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 10, "w": 8, "x": 16, "y": 20}, "id": 6, "options": {"legend": {"calcs": ["min", "max", "mean", "last", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "max by (productCode) (jingway_api_apply_cost_time_seconds{customer=~\"$customer\",productCode=~\"$productCode\",quantile=\"$quantile\"})", "legendFormat": "{{productCode}}", "range": true, "refId": "A"}], "title": "响应时间$quantile分位(按productCode分)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 10, "w": 8, "x": 0, "y": 30}, "id": 10, "options": {"legend": {"calcs": ["min", "max", "mean", "last", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "9.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "(sum by (customer) (rate(jingway_api_apply_cost_time_seconds_sum{customer=~\"$customer\",productCode=~\"$productCode\"}[2m]))) / sum by (customer) (rate(jingway_api_apply_cost_time_seconds_count{customer=~\"$customer\",productCode=~\"$productCode\"}[2m]))", "legendFormat": "{{customer}}", "range": true, "refId": "A"}], "title": "平均响应时间(按customer分)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 10, "w": 8, "x": 8, "y": 30}, "id": 11, "options": {"legend": {"calcs": ["min", "max", "mean", "last", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "max by (customer) (jingway_api_apply_cost_time_seconds_max{customer=~\"$customer\",productCode=~\"$productCode\"})", "legendFormat": "{{customer}}", "range": true, "refId": "A"}], "title": "最大耗时(按customer分)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 10, "w": 8, "x": 16, "y": 30}, "id": 12, "options": {"legend": {"calcs": ["min", "max", "mean", "last", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "max by (customer) (jingway_api_apply_cost_time_seconds{customer=~\"$customer\",productCode=~\"$productCode\",quantile=\"$quantile\"})", "legendFormat": "{{customer}}", "range": true, "refId": "A"}], "title": "响应时间$quantile分位(按customer分)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 10, "w": 8, "x": 0, "y": 40}, "id": 7, "options": {"legend": {"calcs": ["min", "max", "mean", "last", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "9.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "(sum by (customer,productCode) (rate(jingway_api_apply_cost_time_seconds_sum{customer=~\"$customer\",productCode=~\"$productCode\"}[2m]))) / sum by (customer,productCode) (rate(jingway_api_apply_cost_time_seconds_count{customer=~\"$customer\",productCode=~\"$productCode\"}[2m]))", "legendFormat": "{{customer}} - {{productCode}}", "range": true, "refId": "A"}], "title": "平均响应时间(按customer - productCode分)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 10, "w": 8, "x": 8, "y": 40}, "id": 8, "options": {"legend": {"calcs": ["min", "max", "mean", "last", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "max by (customer,productCode) (jingway_api_apply_cost_time_seconds_max{customer=~\"$customer\",productCode=~\"$productCode\"})", "legendFormat": "{{customer}} - {{productCode}}", "range": true, "refId": "A"}], "title": "最大耗时(按customer - productCode分)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 10, "w": 8, "x": 16, "y": 40}, "id": 9, "options": {"legend": {"calcs": ["min", "max", "mean", "last", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "max by (customer,productCode) (jingway_api_apply_cost_time_seconds{customer=~\"$customer\",customer=~\"$customer\",quantile=\"$quantile\"})", "legendFormat": "{{customer}} - {{productCode}}", "range": true, "refId": "A"}], "title": "响应时间$quantile分位(按customer - productCode分)", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 50}, "id": 26, "panels": [], "title": "调用量", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 51}, "id": 18, "options": {"displayLabels": ["percent", "name"], "legend": {"displayMode": "table", "placement": "right", "showLegend": true, "values": ["value", "percent"]}, "pieType": "donut", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "sum by (customer) (jingway_api_apply_cost_time_seconds_count{customer=~\"$customer\",productCode=~\"$productCode\"})", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "调用总量（按customer分）", "type": "piechart"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 51}, "id": 19, "options": {"displayLabels": ["percent", "name"], "legend": {"displayMode": "table", "placement": "right", "showLegend": true, "values": ["value", "percent"]}, "pieType": "donut", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "sum by (productCode) (jingway_api_apply_cost_time_seconds_count{customer=~\"$customer\",productCode=~\"$productCode\"})", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "调用总量（按productCode分）", "type": "piechart"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 51}, "id": 20, "options": {"displayLabels": ["percent", "name"], "legend": {"displayMode": "table", "placement": "right", "showLegend": true, "values": ["value", "percent"]}, "pieType": "donut", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "sum by (customer,productCode) (jingway_api_apply_cost_time_seconds_count{customer=~\"$customer\",productCode=~\"$productCode\"})", "legendFormat": "{{customer}} - {{productCode}}", "range": true, "refId": "A"}], "title": "调用总量（按customer和productCode分）", "type": "piechart"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 59}, "id": 28, "panels": [], "title": "服务节点", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 10, "w": 8, "x": 0, "y": 60}, "id": 31, "options": {"legend": {"calcs": ["min", "max", "mean", "last", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "9.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "sum by (service, function) (rate(jingway_api_service_cost_time_seconds_count{service=~\"$service\",function=~\"$function\"}[2m]))", "legendFormat": "{{service}} - {{function}}", "range": true, "refId": "A"}], "title": "2分钟平均QPS", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 10, "w": 8, "x": 8, "y": 60}, "id": 29, "options": {"legend": {"calcs": ["min", "max", "mean", "last", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "9.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "(sum by (service,function) (rate(jingway_api_service_cost_time_seconds_sum{service=~\"$service\",function=~\"$function\"}[2m]))) / sum by (service,function) (rate(jingway_api_service_cost_time_seconds_count{service=~\"$service\",function=~\"$function\"}[2m]))", "legendFormat": "{{service}} - {{function}}", "range": true, "refId": "A"}], "title": "平均响应时间", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 10, "w": 8, "x": 16, "y": 60}, "id": 30, "options": {"legend": {"calcs": ["min", "max", "mean", "last", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "max by (service,function) (jingway_api_service_cost_time_seconds_max{service=~\"$service\",function=~\"$function\"})", "legendFormat": "{{service}} - {{function}}", "range": true, "refId": "A"}], "title": "最大耗时", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 70}, "id": 33, "panels": [], "title": "其他", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 71}, "id": 34, "options": {"legend": {"calcs": ["min", "max", "mean", "last", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "9.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "sum by (customer, productCode) (rate(jingway_api_apply_timeout_total{customer=~\"$customer\",productCode=~\"$productCode\"}[2m]))", "legendFormat": "{{customer}} - {{productCode}}", "range": true, "refId": "A"}], "title": "超时申请QPS", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 71}, "id": 35, "options": {"legend": {"calcs": ["min", "max", "mean", "last", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "9.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "sum by (serviceType, function) (rate(jingway_api_apply_retry_total{serviceType=~\"$service\",function=~\"$function\"}[2m]))", "legendFormat": "{{serviceType}} - {{function}}", "range": true, "refId": "A"}], "title": "重试申请QPS", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 80}, "id": 36, "options": {"legend": {"calcs": ["min", "max", "mean", "last", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "9.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "sum by (productCode, serviceType, function) (rate(jingway_api_ds_hit_total{productCode=~\"$productCode\"}[2m])) / sum by (productCode, serviceType, function) (rate(jingway_api_ds_total{productCode=~\"$productCode\"}[2m]))", "legendFormat": "{{productCode}} - {{serviceType}} - {{function}}", "range": true, "refId": "A"}], "title": "查得率（2分钟平均）", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 80}, "id": 37, "options": {"legend": {"calcs": ["min", "max", "mean", "last", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "9.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "sum by (productCode, serviceType, function) (rate(jingway_api_ds_total{productCode=~\"$productCode\"}[2m])) - sum by (productCode, serviceType, function) (rate(jingway_api_ds_hit_total{productCode=~\"$productCode\"}[2m]))", "legendFormat": "{{productCode}} - {{serviceType}} - {{function}}", "range": true, "refId": "A"}], "title": "未查得QPS（2分钟平均）", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 89}, "id": 39, "options": {"legend": {"calcs": ["min", "max", "mean", "last"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "asc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "sum by (code) (increase(jingway_api_apply_resp_total{app=\"jingway-api\",code=~\"$code\"}[5m]))", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "业务返回码5分钟增长", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 89}, "id": 41, "options": {"legend": {"calcs": ["max", "min", "last", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "max by (scriptCode) (jingway_api_script_cost_time_seconds_max{app=~\"jingway-api\",scriptCode=~\"$scriptCode\"})", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "脚本执行最大时间（按scriptCode分）", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["{scriptCode=\"Test\"}"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": true}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 97}, "id": 43, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "sum by (scriptCode) (increase(jingway_api_script_error_total{app=\"jingway-api\",scriptCode=~\"$scriptCode\"}[2m]))", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "脚本执行出错2分钟增长", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 97}, "id": 45, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "sum (increase(jingway_api_local_cache_refresh_err_total{}[5m]))", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "缓存刷新出错", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 105}, "id": 47, "options": {"legend": {"calcs": ["max", "min", "mean", "last"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "editorMode": "code", "expr": "max by (type) (jingway_api_apply_time_seconds_max{})", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "处理阶段最大时间", "type": "timeseries"}], "schemaVersion": 37, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "definition": "label_values(jingway_api_apply_cost_time_seconds_sum{  app=\"jingway-api\",customer!=\"none\"}, customer)", "hide": 0, "includeAll": true, "multi": true, "name": "customer", "options": [], "query": {"query": "label_values(jingway_api_apply_cost_time_seconds_sum{  app=\"jingway-api\",customer!=\"none\"}, customer)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "type": "query"}, {"current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "definition": "label_values(jingway_api_apply_cost_time_seconds_sum{  app=\"jingway-api\", customer=~\"$customer\"}, productCode)", "hide": 0, "includeAll": true, "multi": true, "name": "productCode", "options": [], "query": {"query": "label_values(jingway_api_apply_cost_time_seconds_sum{  app=\"jingway-api\", customer=~\"$customer\"}, productCode)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "0.99", "value": "0.99"}, "hide": 0, "includeAll": false, "multi": false, "name": "quantile", "options": [{"selected": true, "text": "0.99", "value": "0.99"}, {"selected": false, "text": "0.95", "value": "0.95"}, {"selected": false, "text": "0.5", "value": "0.5"}], "query": "0.99,0.95,0.5", "queryValue": "", "skipUrlSync": false, "type": "custom"}, {"current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "definition": "label_values(jingway_api_service_cost_time_seconds_sum{  app=\"jingway-api\"}, service)", "hide": 0, "includeAll": true, "label": "service", "multi": true, "name": "service", "options": [], "query": {"query": "label_values(jingway_api_service_cost_time_seconds_sum{  app=\"jingway-api\"}, service)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "definition": "label_values(jingway_api_service_cost_time_seconds_sum{  app=\"jingway-api\", service=~\"$service\"}, function)", "hide": 0, "includeAll": true, "label": "function", "multi": true, "name": "function", "options": [], "query": {"query": "label_values(jingway_api_service_cost_time_seconds_sum{  app=\"jingway-api\", service=~\"$service\"}, function)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "definition": "label_values(jingway_api_apply_resp_total{app=\"jingway-api\"}, code)", "hide": 0, "includeAll": true, "label": "返回码", "multi": true, "name": "code", "options": [], "query": {"query": "label_values(jingway_api_apply_resp_total{app=\"jingway-api\"}, code)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "prometheus", "uid": "aeq74uc72fdhcd"}, "definition": "label_values(jingway_api_script_cost_time_seconds_sum{app=\"jingway-api\"}, scriptCode)", "hide": 0, "includeAll": true, "label": "脚本编码", "multi": true, "name": "scriptCode", "options": [], "query": {"query": "label_values(jingway_api_script_cost_time_seconds_sum{app=\"jingway-api\"}, scriptCode)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "type": "query"}]}, "time": {"from": "now-2h", "to": "now"}, "timepicker": {"refresh_intervals": ["5m", "15m", "30m"]}, "timezone": "", "title": "jingway-api", "uid": "Kn_EiEbVz", "version": 57, "weekStart": ""}