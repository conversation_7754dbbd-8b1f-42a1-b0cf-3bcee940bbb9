[client]
port = 3306
socket = /var/lib/mysql/run/mysql.sock

[mysqld]
# Basic Settings
server_id = 17 # Master用17, Slave用18, 请手动指定!
report_host = mysql-master # 建议手动指定
port = 3306
user = mysql
basedir = /usr # RPM包的basedir通常是/usr
datadir = /var/lib/mysql/data
tmpdir = /var/lib/mysql/tmp
socket = /var/lib/mysql/run/mysql.sock
pid_file = /var/lib/mysql/run/mysql.pid

# Character Set
character_set_server = utf8mb4
collation_server = utf8mb4_general_ci
init_connect = 'SET NAMES utf8mb4'

# Behavior & Limits
lower_case_table_names = 1
skip_name_resolve = 1
explicit_defaults_for_timestamp = ON # 推荐ON
sql_mode = STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION

# Connection Settings
max_connections = 500 # 关键调整
max_user_connections = 400
max_connect_errors = 99999999
interactive_timeout = 28800
wait_timeout = 3600 # 关键调整
back_log = 150 # 根据 max_connections 调整

# Buffer & Cache Settings (Per-Session)
# 关键调整区域，避免内存溢出
read_buffer_size = 2M
read_rnd_buffer_size = 8M
sort_buffer_size = 8M
binlog_cache_size = 1M

# Table & Thread Cache
table_open_cache = 4096
thread_cache_size = 64

# MyISAM (保留基本配置)
key_buffer_size = 64M

# Logging
log_error = /var/lib/mysql/log/error.log
slow_query_log = 1
long_query_time = 1
slow_query_log_file = /var/lib/mysql/log/slow.log
log_queries_not_using_indexes = 1

# Binary Log & Replication
log_bin = /var/lib/mysql/log/mysql-bin
binlog_format = ROW
sync_binlog = 1
expire_logs_days = 7 # 建议延长至7天
log_slave_updates = 1

# GTID
gtid_mode = ON
enforce_gtid_consistency = ON
master_info_repository = TABLE
relay_log_info_repository = TABLE
relay_log = /var/lib/mysql/log/mysql-relay-bin
relay_log_recovery = ON
skip_slave_start = ON

# InnoDB Settings
default-storage-engine = InnoDB
innodb_file_per_table = 1
innodb_open_files = 4096
# InnoDB Core - 关键优化
innodb_buffer_pool_size = 10G # 关键调整
innodb_buffer_pool_instances = 8 # 关键调整
# InnoDB Log
innodb_log_file_size = 512M
innodb_log_buffer_size = 16M # 可适当增大
# InnoDB I/O
innodb_flush_method = O_DIRECT
innodb_write_io_threads = 4
innodb_read_io_threads = 4
innodb_flush_log_at_trx_commit = 1
# InnoDB Misc
innodb_lock_wait_timeout = 50
innodb_strict_mode = 1