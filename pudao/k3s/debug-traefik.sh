#!/bin/bash

echo "=== K3S Traefik 服务暴露问题诊断脚本 ==="
echo

echo "1. 检查 HelmChartConfig 状态："
kubectl -n kube-system get helmchartconfig traefik -o yaml
echo

echo "2. 检查 HelmChart 状态："
kubectl -n kube-system get helmchart traefik -o yaml
echo

echo "3. 检查 Traefik Service 当前配置："
kubectl -n kube-system get svc traefik -o yaml
echo

echo "4. 检查 Traefik Pod 状态："
kubectl -n kube-system get pods -l app.kubernetes.io/name=traefik
echo

echo "5. 检查 Traefik Pod 日志（最近50行）："
kubectl -n kube-system logs -l app.kubernetes.io/name=traefik --tail=50
echo

echo "6. 检查 klipper-lb 相关 Pod："
kubectl -n kube-system get pods -l app=svclb-traefik
echo

echo "7. 检查 IngressRoute CRD 是否存在："
kubectl get crd ingressroutes.traefik.io
echo

echo "8. 检查 IngressRoute 资源："
kubectl get ingressroutes -A
echo

echo "9. 检查节点端口监听情况（在任意节点执行）："
echo "请在节点上执行: netstat -tlnp | grep -E ':(8001|8002|8003|8004|8005|8006|8007|8008|8009|8013|3000|8899|9090)'"
echo

echo "=== 诊断完成 ==="
