#!/bin/bash

echo "=== 修复 Traefik 多端口配置 ==="
echo

echo "步骤1: 删除现有的 HelmChartConfig（如果存在）"
kubectl -n kube-system delete helmchartconfig traefik --ignore-not-found=true
echo "等待5秒..."
sleep 5

echo "步骤2: 重新应用 HelmChartConfig"
kubectl -n kube-system apply -f k3s/traefik/traefik-helmchartconfig.yaml
echo

echo "步骤3: 检查 HelmChartConfig 是否创建成功"
kubectl -n kube-system get helmchartconfig traefik
echo

echo "步骤4: 强制重启 Traefik Pod 以应用新配置"
kubectl -n kube-system delete pods -l app.kubernetes.io/name=traefik
echo

echo "步骤5: 等待 Traefik Pod 重新启动（最多等待60秒）"
kubectl -n kube-system wait --for=condition=ready pod -l app.kubernetes.io/name=traefik --timeout=60s
echo

echo "步骤6: 检查 Traefik Service 配置"
kubectl -n kube-system get svc traefik -o yaml | grep -A 20 "spec:"
echo

echo "步骤7: 检查 klipper-lb Pod 状态"
kubectl -n kube-system get pods -l app=svclb-traefik
echo

echo "步骤8: 应用 IngressRoute 配置"
kubectl apply -f k3s/ingress/ingressroutes.yaml
echo

echo "步骤9: 检查 IngressRoute 状态"
kubectl get ingressroutes -A
echo

echo "=== 修复完成，请等待1-2分钟让配置完全生效 ==="
echo

echo "验证命令："
echo "kubectl -n kube-system get svc traefik"
echo "kubectl -n kube-system get pods -l app=svclb-traefik"
echo "netstat -tlnp | grep -E ':(8001|8002|8003|8004|8005|8006|8007|8008|8009|8013|3000|8899|9090)'"
