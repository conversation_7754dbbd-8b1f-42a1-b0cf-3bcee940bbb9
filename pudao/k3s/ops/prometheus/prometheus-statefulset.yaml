apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: prometheus
  namespace: ops
spec:
  selector:
    matchLabels:
      app: prometheus
  serviceName: prometheus
  replicas: 1
  template:
    metadata:
      labels:
        app: prometheus
    spec:
      serviceAccountName: prometheus
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: node-type
                operator: In
                values: ["ops"]
      containers:
      - name: prometheus
        image: docker-registry.local:5000/prometheus:v3.4.1
        args:
          - --config.file=/etc/prometheus/prometheus.yml
          - --storage.tsdb.path=/prometheus
          - --storage.tsdb.retention.time=15d
        ports:
          - containerPort: 9090
            name: http
        volumeMounts:
          - name: config
            mountPath: /etc/prometheus
          - name: data
            mountPath: /prometheus
      volumes:
        - name: config
          configMap:
            name: prometheus-config
  volumeClaimTemplates:
  - metadata:
      name: data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 20Gi
---
apiVersion: v1
kind: Service
metadata:
  name: prometheus
  namespace: ops
spec:
  selector:
    app: prometheus
  ports:
    - name: http
      port: 9090
      targetPort: 9090
  type: ClusterIP

