apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: ops
data:
  prometheus.yml: |
    global:
      scrape_interval: 30s
      evaluation_interval: 30s
    scrape_configs:
      - job_name: 'kubernetes-pods'
        kubernetes_sd_configs:
          - role: pod
        relabel_configs:
          - action: keep
            source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
            regex: true
          - action: replace
            source_labels: [__meta_kubernetes_pod_ip, __meta_kubernetes_pod_annotation_prometheus_io_port]
            regex: (.+);(\d+)
            target_label: __address__
            replacement: ${1}:${2}
          - action: labelmap
            regex: __meta_kubernetes_pod_label_(.+)

