apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: loki
  namespace: ops
spec:
  selector:
    matchLabels:
      app: loki
  serviceName: loki
  replicas: 1
  template:
    metadata:
      labels:
        app: loki
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: node-type
                operator: In
                values: ["ops"]
      containers:
      - name: loki
        image: docker-registry.local:5000/loki:3.5.1
        args:
          - -config.file=/etc/loki/loki.yaml
        ports:
          - containerPort: 3100
            name: http
        volumeMounts:
          - name: config
            mountPath: /etc/loki
          - name: data
            mountPath: /loki
      volumes:
        - name: config
          configMap:
            name: loki-config
  volumeClaimTemplates:
  - metadata:
      name: data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 20Gi
---
apiVersion: v1
kind: Service
metadata:
  name: loki
  namespace: ops
spec:
  selector:
    app: loki
  ports:
    - name: http
      port: 3100
      targetPort: 3100
  type: ClusterIP

