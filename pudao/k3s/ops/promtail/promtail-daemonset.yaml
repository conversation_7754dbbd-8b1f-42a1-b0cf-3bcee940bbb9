apiVersion: v1
kind: ConfigMap
metadata:
  name: promtail-config
  namespace: ops
data:
  promtail.yaml: |
    server:
      http_listen_port: 9080
      grpc_listen_port: 0
    positions:
      filename: /var/lib/promtail/positions.yaml
    clients:
      - url: http://loki.ops.svc:3100/loki/api/v1/push
    scrape_configs:
      - job_name: kubernetes-pods
        kubernetes_sd_configs:
          - role: pod
        pipeline_stages:
          - docker: {}
        relabel_configs:
          - source_labels: ['__meta_kubernetes_pod_node_name']
            target_label: 'node'
          - source_labels: ['__meta_kubernetes_namespace']
            target_label: 'namespace'
          - source_labels: ['__meta_kubernetes_pod_name']
            target_label: 'pod'
          - source_labels: ['__meta_kubernetes_pod_container_name']
            target_label: 'container'
          - action: replace
            source_labels: ['__meta_kubernetes_pod_container_name']
            target_label: __path__
            replacement: /var/log/containers/*$1*.log
---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: promtail
  namespace: ops
  labels:
    app: promtail
spec:
  selector:
    matchLabels:
      app: promtail
  template:
    metadata:
      labels:
        app: promtail
    spec:
      serviceAccountName: promtail
      containers:
      - name: promtail
        image: docker-registry.local:5000/promtail:3.5.1
        args: ["-config.file=/etc/promtail/promtail.yaml"]
        volumeMounts:
          - name: config
            mountPath: /etc/promtail
          - name: varlog
            mountPath: /var/log
          - name: positions
            mountPath: /var/lib/promtail
        securityContext:
          readOnlyRootFilesystem: true
      volumes:
        - name: config
          configMap:
            name: promtail-config
        - name: varlog
          hostPath:
            path: /var/log
        - name: positions
          hostPath:
            path: /var/lib/promtail
            type: DirectoryOrCreate
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: promtail
  namespace: ops
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: promtail
rules:
  - apiGroups: [""]
    resources: ["pods", "pods/log", "namespaces"]
    verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: promtail
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: promtail
subjects:
  - kind: ServiceAccount
    name: promtail
    namespace: ops

