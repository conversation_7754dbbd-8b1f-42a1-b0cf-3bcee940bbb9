apiVersion: helm.cattle.io/v1
kind: HelmChartConfig
metadata:
  name: traefik
  namespace: kube-system
spec:
  valuesContent: |
    ports:
      principal-in:
        port: 8001
        expose: true
        exposedPort: 8001
        protocol: TCP
      tako-in:
        port: 8002
        expose: true
        exposedPort: 8002
        protocol: TCP
      mflow-in:
        port: 8003
        expose: true
        exposedPort: 8003
        protocol: TCP
      jingway-in:
        port: 8004
        expose: true
        exposedPort: 8004
        protocol: TCP
      statistics-in:
        port: 8005
        expose: true
        exposedPort: 8005
        protocol: TCP
      argus-api:
        port: 8006
        expose: true
        exposedPort: 8006
        protocol: TCP
      gateway-api:
        port: 8007
        expose: true
        exposedPort: 8007
        protocol: TCP
      tako-api:
        port: 8008
        expose: true
        exposedPort: 8008
        protocol: TCP
      jingway-api:
        port: 8009
        expose: true
        exposedPort: 8009
        protocol: TCP
      zeus:
        port: 8013
        expose: true
        exposedPort: 8013
        protocol: TCP
      grafana:
        port: 3000
        expose: true
        exposedPort: 3000
        protocol: TCP
      elastic-job-console:
        port: 8899
        expose: true
        exposedPort: 8899
        protocol: TCP
      prometheus:
        port: 9090
        expose: true
        exposedPort: 9090
        protocol: TCP

    service:
      type: <PERSON><PERSON><PERSON><PERSON><PERSON>
