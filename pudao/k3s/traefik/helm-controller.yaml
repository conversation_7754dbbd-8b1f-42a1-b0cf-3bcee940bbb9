apiVersion: v1
kind: ServiceAccount
metadata:
  name: helm-controller
  namespace: kube-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: helm-controller
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cluster-admin
subjects:
- kind: ServiceAccount
  name: helm-controller
  namespace: kube-system
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: helm-controller
  namespace: kube-system
spec:
  replicas: 1
  selector:
    matchLabels:
      app: helm-controller
  template:
    metadata:
      labels:
        app: helm-controller
    spec:
      serviceAccountName: helm-controller
      containers:
      - name: helm-controller
        image: docker-registry.local:5000/rancher/klipper-helm:v0.9.8-build20250709
        imagePullPolicy: IfNotPresent
        args:
        - --namespace=kube-system
        - --helm-driver=secret
        - --enable-leader-election