# 从 docker swarm 迁移到 K3S

## 核心组件与概念总览

### 关键对象与作用

- <PERSON><PERSON><PERSON><PERSON>（Ingress Controller，K3S 默认）：负责接收外部端口流量并根据路由规则转发到集群内服务/Pod。
- HelmChart / HelmChartConfig（helm.cattle.io CRD）：K3S 通过内置 HelmChart 安装 Traefik；我们用 `HelmChartConfig`（文件：`k3s/traefik/traefik-helmchartconfig.yaml`）覆盖 Traefik 的 values，声明多组 entrypoints（端口）。
- Ingress / IngressRoute：K8s 标准 Ingress 由控制器解析；Traefik 还提供自有 CRD IngressRoute（文件：`k3s/ingress/ingressroutes.yaml`），可精确指定 entryPoints 和后端 Service/端口。本迁移采用 IngressRoute。
- Service（ClusterIP）与 Deployment/StatefulSet：业务与运维工作负载通过 Service 暴露集群内端口；IngressRoute 再把外部端口映射到这些 Service。

### K3S 流量与控制关系图（Mermaid）

```mermaid
graph TD
  A["云桌面/客户端\n访问: NodeIP:端口(8001..8013,3000,8899,9090)"] --> B["Traefik Service\n(type: LoadBalancer - klipper-lb)\n在各节点监听对外端口"]
  B --> C["Traefik Ingress Controller (Pods)\nNamespace: kube-system"]
  C -->|监听/解析| D["IngressRoute CRs\n(k3s/ingress/ingressroutes.yaml)"]
  D -->|路由到| E["K8s Service (ClusterIP)\napp/* 服务"]
  E --> F["App Pods (Deployments)\nNamespace: app"]
  D -->|路由到| G["K8s Service (ClusterIP)\nops/* 服务"]
  G --> H["Ops Pods (StatefulSet/Deployment)\nNamespace: ops"]

  subgraph "控制面配置"
    HC["HelmChart(traefik)\nNamespace: kube-system"] --> HCC["HelmChartConfig\n(k3s/traefik/traefik-helmchartconfig.yaml)\nvaluesContent: ports(entrypoints)"]
    HCC -.覆盖Traefik配置.-> C
  end
```

### 协同工作机制（端口直连，无域名）

1. 应用 `HelmChartConfig`：`kubectl -n kube-system apply -f k3s/traefik/traefik-helmchartconfig.yaml`，为 Traefik 开启多端口 entrypoints（如 8001/8002/.../8013、3000、8899、9090）。
2. 部署 App 与 Ops：为每个服务创建 `Deployment/StatefulSet` 与 `Service(ClusterIP)`。
3. 应用 IngressRoute：在 `k3s/ingress/ingressroutes.yaml` 中为每个对外服务绑定对应 entryPoints，将外部端口映射到 `Service:port`。
4. 访问路径：客户端 → NodeIP:端口 → Traefik Service（klipper-lb 暴露）→ Traefik Pods → IngressRoute 路由 → Service → Pod。

注意：
- entrypoint 名称必须与 IngressRoute `spec.entryPoints` 完全一致（本仓库已使用完整服务名，避免歧义）。
- 多控制面时任一 server 节点执行 `kubectl apply` 即可生效；agent 节点无需操作。
- 修改 `HelmChartConfig` 后再次 `kubectl apply`，Traefik 将自动滚动更新以生效。


## 准备物料

```sh
# 下载 k3s 安装二进制
https://github.com/k3s-io/k3s/releases/download/v1.33.3+k3s1/k3s
# 下载 Airgap 镜像包（包含 k3s core 及所有组件镜像）
wget https://github.com/k3s-io/k3s/releases/download/v1.33.3+k3s1/k3s-airgap-images-amd64.tar.gz
# 下载 kubectl
wget "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
# 下载 crictl
wget https://github.com/kubernetes-sigs/cri-tools/releases/download/v1.33.0/crictl-v1.33.0-linux-amd64.tar.gz
```

## Docker Swarm 环境清理

```sh
docker stack ls
docker stack rm app
docker stack rm ops

# 离开swarm集群
docker swarm leave --force  # 在manager节点执行
docker swarm leave          # 在worker节点执行

docker network prune
docker volume prune

```

## 卸载k3s

```sh
systemctl stop k3s
systemctl stop containerd
pkill -9 -f "k3s|containerd"
mount | grep -E 'k3s|kubelet' | awk '{ print $3 }' | xargs -r umount

rm -rf /etc/rancher/k3s /var/lib/rancher/k3s /run/k3s /var/lib/cni /etc/cni /var/lib/kubelet
```

## 安装 k3s

### 控制节点 ************

```sh
cp k3s /usr/local/bin/
chmod +x /usr/local/bin/k3s
mkdir -p /var/lib/rancher/k3s/agent/images/
cp /home/<USER>/k3s/k3s/k3s-airgap-images-amd64.tar.gz /var/lib/rancher/k3s/agent/images/

# 创建K3S配置目录
mkdir -p /etc/rancher/k3s

# 配置k3s
cat > /etc/rancher/k3s/config.yaml << EOF
cluster-init: true
token: "k3s-token"
tls-san:
  - ************
  - ************  
  - ************
 # 保持启用 Traefik 以用于按端口暴露（多 entrypoints）
write-kubeconfig-mode: "0644"
node-label:
  - "node-type=app"
EOF


# 使用systemd
cat > /etc/systemd/system/k3s.service << EOF
[Unit]
Description=Lightweight Kubernetes
Documentation=https://k3s.io
Wants=network-online.target
After=network-online.target

[Service]
Type=exec
ExecStart=/usr/local/bin/k3s server
KillMode=process
Delegate=yes
LimitNOFILE=1048576
LimitNPROC=infinity
LimitCORE=infinity
TasksMax=infinity
TimeoutStartSec=0
Restart=always
RestartSec=5s

[Install]
WantedBy=multi-user.target
EOF


# 启动k3s
systemctl daemon-reload
systemctl enable --now k3s
systemctl status k3s
systemctl start k3s
systemctl restart k3s


```

### 加入其他控制节点 ************ / ************

```sh
cp k3s /usr/local/bin/
chmod +x /usr/local/bin/k3s
mkdir -p /var/lib/rancher/k3s/agent/images/
cp k3s-airgap-images-amd64.tar.gz /var/lib/rancher/k3s/agent/images/

# 创建K3S配置目录
mkdir -p /etc/rancher/k3s
cat > /etc/rancher/k3s/config.yaml << EOF
server: https://************:6443
token: "k3s-token"
tls-san:
  - ************
  - ************
  - ************  
 # 保持启用 Traefik 以用于按端口暴露（多 entrypoints）
write-kubeconfig-mode: "0644"
node-label:
  - "node-type=app"
EOF


# 使用systemd
cat > /etc/systemd/system/k3s.service << EOF
[Unit]
Description=Lightweight Kubernetes
Documentation=https://k3s.io
Wants=network-online.target
After=network-online.target

[Service]
Type=exec
ExecStart=/usr/local/bin/k3s server
KillMode=process
Delegate=yes
LimitNOFILE=1048576
LimitNPROC=infinity
LimitCORE=infinity
TasksMax=infinity
TimeoutStartSec=0
Restart=always
RestartSec=5s

[Install]
WantedBy=multi-user.target
EOF


# 启动k3s
systemctl daemon-reload
systemctl enable --now k3s
systemctl status k3s
# systemctl start k3s

```

### 加入其他agent节点 ************


```sh
cp k3s /usr/local/bin/
chmod +x /usr/local/bin/k3s
mkdir -p /var/lib/rancher/k3s/agent/images/
cp k3s-airgap-images-amd64.tar.gz /var/lib/rancher/k3s/agent/images/

# 创建K3S配置目录
mkdir -p /etc/rancher/k3s
cat > /etc/rancher/k3s/config.yaml << EOF
server: https://************:6443
token: "k3s-token"
node-label:
  - "node-type=ops"
EOF


# 使用systemd
cat > /etc/systemd/system/k3s.service << EOF
[Unit]
Description=Lightweight Kubernetes
Documentation=https://k3s.io
Wants=network-online.target
After=network-online.target

[Service]
Type=exec
ExecStart=/usr/local/bin/k3s agent
KillMode=process
Delegate=yes
LimitNOFILE=1048576
LimitNPROC=infinity
LimitCORE=infinity
TasksMax=infinity
TimeoutStartSec=0
Restart=always
RestartSec=5s

[Install]
WantedBy=multi-user.target
EOF


# 启动k3s
systemctl daemon-reload
systemctl enable --now k3s
systemctl status k3s
# systemctl start k3s
# systemctl restart k3s

```

### 验证

```sh
cp kubectl /usr/local/sbin/
chmod +x /usr/local/sbin/kubectl

tar -zxvf crictl-v1.33.0-linux-amd64.tar.gz
cp crictl /usr/local/sbin/
chmod +x /usr/local/sbin/crictl

tee -a /etc/profile <<EOF
export KUBECONFIG=/etc/rancher/k3s/k3s.yaml
EOF
source /etc/profile

kubectl get nodes

```


### 配置私有镜像仓库地址

在所有节点创建镜像仓库配置

```sh
mkdir -p /etc/rancher/k3s
cat > /etc/rancher/k3s/registries.yaml << EOF
mirrors:
  docker-registry.local:5000:
    endpoint:
      - "http://docker-registry.local:5000"
configs:
  docker-registry.local:5000:
    tls:
      insecure_skip_verify: true
EOF

# 重启K3S服务使配置生效
systemctl restart k3s
systemctl status k3s

# 测试镜像拉取
kubectl run test-registry --image=docker-registry.local:5000/tako-api:1.2.3 --rm -it --restart=Never -- echo "Registry test successful"

cat > /etc/crictl.yaml << EOF
runtime-endpoint: unix:///run/k3s/containerd/containerd.sock
image-endpoint: unix:///run/k3s/containerd/containerd.sock
timeout: 10
debug: false
EOF

# 查看containerd配置是否生效
crictl info | grep -A 10 registry

```



## 启用 Traefik 多端口（无域名，端口直连）

```sh
# 直接应用 Traefik 覆写（推荐做法，无需重启）
kubectl -n kube-system apply -f k3s/traefik/traefik-helmchartconfig.yaml

# 验证 Traefik 与端口（需在宿主机侧做端口映射策略）
kubectl -n kube-system get pods -l app.kubernetes.io/name=traefik
kubectl -n kube-system get svc traefik -o wide
```

说明：
- 多控制面时，在任意一台 server 节点执行一次 `kubectl apply` 即可（资源写入集群，由控制器统一分发）；agent 节点无需执行。
- 修改 `traefik-helmchartconfig.yaml` 后再次 `kubectl apply`，Traefik 将自动滚动更新以生效。
- 如遇 IngressRoute CRD 未注册错误，等待 Traefik 初始化完成后再应用 IngressRoute。

多 entrypoints 已在该配置中开启（entrypoint→端口）：
- principal-in→8001
- tako-in→8002
- mflow-in→8003
- jingway-in→8004
- statistics-in→8005
- argus-api→8006
- gateway-api→8007
- tako-api→8008
- jingway-api→8009
- zeus→8013
- grafana→3000，elastic-job-console→8899，prometheus→9090

说明：使用 HelmChartConfig 是 K3S 官方覆盖内置 HelmChart values 的方式，不需要安装 Helm CLI。

## 部署业务应用（先部署 App）

你可能已经创建了 `app` 命名空间并部署了 `tako-api` 与 `elastic-job-console`，下面补齐其余服务并创建端口暴露。

```sh
kubectl apply -f k3s/namespaces.yaml

# 节点标签（如未设置）
# kubectl label nodes ************ node-type=app --overwrite
# kubectl label nodes ************ node-type=app --overwrite
# kubectl label nodes ************ node-type=app --overwrite

# 应用工作负载（按需挑选执行）
kubectl apply -f k3s/app/gateway-api-deployment.yaml
kubectl apply -f k3s/app/jingway-api-deployment.yaml
kubectl apply -f k3s/app/zeus-deployment.yaml
kubectl apply -f k3s/app/principal-in-deployment.yaml
kubectl apply -f k3s/app/tako-in-deployment.yaml
kubectl apply -f k3s/app/mflow-in-deployment.yaml
kubectl apply -f k3s/app/jingway-in-deployment.yaml
kubectl apply -f k3s/app/statistics-in-deployment.yaml
kubectl apply -f k3s/app/argus-api-deployment.yaml

# 如需要：
# kubectl apply -f k3s/tako-api-deployment.yaml
# kubectl apply -f k3s/elastic-job-console-deployment.yaml

# 创建对外端口映射（Traefik IngressRoute）
kubectl apply -f k3s/ingress/ingressroutes.yaml

# 验证（示例）
kubectl -n app get pods -o wide
curl -s http://************:8008/Status/Version | cat   # tako-api
curl -s http://************:8009/Status/Version | cat   # jingway-api
curl -I  http://************:8013                      # zeus
curl -s http://************:8007/Status/Version | cat   # gateway-api
curl -I  http://************:8899                      # elastic-job-console
```

## 部署运维应用

```sh
kubectl create namespace ops
```

### 部署 Loki / Promtail / Prometheus / Grafana（ops）

```sh
kubectl apply -f k3s/namespaces.yaml

# 节点标签
kubectl label nodes ************ node-type=ops --overwrite
kubectl label nodes ************ node-type=app --overwrite
kubectl label nodes ************ node-type=app --overwrite
kubectl label nodes ************ node-type=app --overwrite

# Loki
kubectl apply -f k3s/ops/loki/loki-configmap.yaml
kubectl apply -f k3s/ops/loki/loki-statefulset.yaml

# Promtail
kubectl apply -f k3s/ops/promtail/promtail-daemonset.yaml

# Prometheus
kubectl apply -f k3s/ops/prometheus/prometheus-rbac.yaml
kubectl apply -f k3s/ops/prometheus/prometheus-configmap.yaml
kubectl apply -f k3s/ops/prometheus/prometheus-statefulset.yaml

# Grafana
kubectl apply -f k3s/ops/grafana/grafana-deployment.yaml

# 对外暴露（Traefik IngressRoute 按端口）
kubectl apply -f k3s/ingress/ingressroutes.yaml
```

### 验证

```sh
# Pods Ready
kubectl -n ops get pods -o wide

# Prometheus Targets
curl -s http://************:9090/targets | head -n 20 | cat

# Grafana 访问
curl -I http://************:3000

# 应用访问（示例：tako-api）
curl -s http://************:8008/Status/Version | cat
```
