apiVersion: apps/v1
kind: Deployment
metadata:
  name: jingway-api
  namespace: app
  labels:
    app: jingway-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: jingway-api
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: jingway-api
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9099"
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: node-type
                operator: In
                values: ["app"]
        # 在同主机名(topology.kubernetes.io/hostname)维度尽量分散，避免副本集中到同一节点
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                topologyKey: kubernetes.io/hostname
                labelSelector:
                  matchLabels:
                    app: jingway-api
      # 跨节点均衡：确保 3 台节点上的副本数相差不超过 1
      topologySpreadConstraints:
        - maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: DoNotSchedule
          labelSelector:
            matchLabels:
              app: jingway-api
      hostAliases:
        - ip: "***********"
          hostnames: ["middleware-01"]
        - ip: "***********"
          hostnames: ["middleware-02"]
        - ip: "***********"
          hostnames: ["middleware-03"]
        - ip: "***********"
          hostnames: ["clickhouse"]
        - ip: "************"
          hostnames: ["mysql"]
      containers:
        - name: jingway-api
          image: docker-registry.local:5000/jingway-api:1.2.4
          ports:
            - name: http
              containerPort: 8009
            - name: metrics
              containerPort: 9099
          env:
            - name: SPRING_APPLICATION_JSON
              value: '{"spring.profiles.active":"prod"}'
            - name: TZ
              value: "Asia/Shanghai"
            - name: JAVA_TOOL_OPTIONS
              value: "-Duser.timezone=Asia/Shanghai"
            - name: BPL_JVM_HEAP_SIZE
              value: "4096M"
            - name: BPL_JVM_JVM_OPTIONS
              value: "-XX:+UseG1GC -XX:MaxMetaspaceSize=256M -Dfile.encoding=UTF-8"
          resources:
            requests:
              memory: "4Gi"
              cpu: "500m"
            limits:
              memory: "6Gi"
              cpu: "4"
          livenessProbe:
            exec:
              command: ["curl","-f","http://localhost:8009/Status/Version"]
            initialDelaySeconds: 40
            periodSeconds: 30
            timeoutSeconds: 10
            failureThreshold: 3
          readinessProbe:
            exec:
              command: ["curl","-f","http://localhost:8009/Status/Version"]
            initialDelaySeconds: 40
            periodSeconds: 30
            timeoutSeconds: 10
            failureThreshold: 3
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: jingway-api
  namespace: app
spec:
  type: ClusterIP
  selector:
    app: jingway-api
  ports:
    - name: http
      protocol: TCP
      port: 8009
      targetPort: 8009
    - name: metrics
      protocol: TCP
      port: 9099
      targetPort: 9099

