apiVersion: apps/v1
kind: Deployment
metadata:
  name: elastic-job-console
  namespace: app
  labels:
    app: elastic-job-console
spec:
  replicas: 1
  selector:
    matchLabels:
      app: elastic-job-console
  template:
    metadata:
      labels:
        app: elastic-job-console
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: node-type
                operator: In
                values: ["app"]
      hostAliases:
        - ip: "***********"
          hostnames: ["middleware-01"]
        - ip: "***********"
          hostnames: ["middleware-02"]
        - ip: "***********"
          hostnames: ["middleware-03"]
      containers:
        - name: elastic-job-console
          image: docker-registry.local:5000/elastic-job-lite-console:latest
          ports:
            - name: http
              containerPort: 8899
          env:
            - name: ZK_CONNECTION_STRING
              value: "middleware-01:2181,middleware-02:2181,middleware-03:2181"
            - name: ELASTIC_ROOT_USERNAME
              value: "root"
            - name: ELASTIC_ROOT_PASSWORD
              value: "root"
          resources:
            requests:
              memory: "256Mi"
              cpu: "50m"
            limits:
              memory: "512Mi"
              cpu: "500m"
          livenessProbe:
            tcpSocket:
              port: 8899
            initialDelaySeconds: 15
            periodSeconds: 20
          readinessProbe:
            tcpSocket:
              port: 8899
            initialDelaySeconds: 5
            periodSeconds: 10

      restartPolicy: Always

---

apiVersion: v1
kind: Service
metadata:
  name: elastic-job-console
  namespace: app
spec:
  type: ClusterIP
  selector:
    app: elastic-job-console
  ports:
    - name: http
      protocol: TCP
      port: 8899
      targetPort: 8899

