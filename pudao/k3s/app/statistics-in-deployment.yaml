apiVersion: apps/v1
kind: Deployment
metadata:
  name: statistics-in
  namespace: app
  labels:
    app: statistics-in
spec:
  replicas: 1
  selector:
    matchLabels:
      app: statistics-in
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: statistics-in
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9095"
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: node-type
                operator: In
                values: ["app"]
        # 批处理任务尽量打散，降低同节点资源争用
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                topologyKey: kubernetes.io/hostname
                labelSelector:
                  matchLabels:
                    app: statistics-in
      topologySpreadConstraints:
        - maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: DoNotSchedule
          labelSelector:
            matchLabels:
              app: statistics-in
      hostAliases:
        - ip: "***********"
          hostnames: ["middleware-01"]
        - ip: "***********"
          hostnames: ["middleware-02"]
        - ip: "***********"
          hostnames: ["middleware-03"]
        - ip: "***********"
          hostnames: ["clickhouse"]
        - ip: "************"
          hostnames: ["mysql"]
      containers:
        - name: statistics-in
          image: docker-registry.local:5000/statistics-in:1.2.3
          ports:
            - name: http
              containerPort: 8005
            - name: metrics
              containerPort: 9095
          env:
            - name: SPRING_APPLICATION_JSON
              value: '{"spring.profiles.active":"prod"}'
            - name: TZ
              value: "Asia/Shanghai"
            - name: JAVA_TOOL_OPTIONS
              value: "-Duser.timezone=Asia/Shanghai"
            - name: BPL_JVM_HEAP_SIZE
              value: "4096M"
            - name: BPL_JVM_JVM_OPTIONS
              value: "-XX:+UseG1GC -XX:MaxMetaspaceSize=256M -Dfile.encoding=UTF-8"
          resources:
            requests:
              memory: "4Gi"
              cpu: "500m"
            limits:
              memory: "6Gi"
              cpu: "4"
          livenessProbe:
            exec:
              command: ["curl","-f","http://localhost:8005/Status/Version"]
            initialDelaySeconds: 40
            periodSeconds: 30
            timeoutSeconds: 10
            failureThreshold: 3
          readinessProbe:
            exec:
              command: ["curl","-f","http://localhost:8005/Status/Version"]
            initialDelaySeconds: 40
            periodSeconds: 30
            timeoutSeconds: 10
            failureThreshold: 3
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: statistics-in
  namespace: app
spec:
  type: ClusterIP
  selector:
    app: statistics-in
  ports:
    - name: http
      protocol: TCP
      port: 8005
      targetPort: 8005
    - name: metrics
      protocol: TCP
      port: 9095
      targetPort: 9095

