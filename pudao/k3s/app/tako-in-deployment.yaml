apiVersion: apps/v1
kind: Deployment
metadata:
  name: tako-in
  namespace: app
  labels:
    app: tako-in
spec:
  replicas: 1
  selector:
    matchLabels:
      app: tako-in
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: tako-in
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9002"
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: node-type
                operator: In
                values: ["app"]
      hostAliases:
        - ip: "***********"
          hostnames: ["middleware-01"]
        - ip: "***********"
          hostnames: ["middleware-02"]
        - ip: "***********"
          hostnames: ["middleware-03"]
        - ip: "***********"
          hostnames: ["clickhouse"]
        - ip: "************"
          hostnames: ["mysql"]
      containers:
        - name: tako-in
          image: docker-registry.local:5000/tako-in:1.2.3
          ports:
            - name: http
              containerPort: 8002
            - name: metrics
              containerPort: 9002
          env:
            - name: SPRING_APPLICATION_JSON
              value: '{"spring.profiles.active":"prod"}'
            - name: TZ
              value: "Asia/Shanghai"
            - name: JAVA_TOOL_OPTIONS
              value: "-Duser.timezone=Asia/Shanghai"
            - name: BPL_JVM_HEAP_SIZE
              value: "1536M"
            - name: BPL_JVM_JVM_OPTIONS
              value: "-XX:+UseG1GC -XX:MaxMetaspaceSize=256M -Dfile.encoding=UTF-8"
          resources:
            requests:
              memory: "1536Mi"
              cpu: "50m"
            limits:
              memory: "2Gi"
              cpu: "1"
          livenessProbe:
            exec:
              command: ["curl","-f","http://localhost:8002/Status/Version"]
            initialDelaySeconds: 40
            periodSeconds: 30
            timeoutSeconds: 10
            failureThreshold: 3
          readinessProbe:
            exec:
              command: ["curl","-f","http://localhost:8002/Status/Version"]
            initialDelaySeconds: 40
            periodSeconds: 30
            timeoutSeconds: 10
            failureThreshold: 3
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: tako-in
  namespace: app
spec:
  type: ClusterIP
  selector:
    app: tako-in
  ports:
    - name: http
      protocol: TCP
      port: 8002
      targetPort: 8002
    - name: metrics
      protocol: TCP
      port: 9002
      targetPort: 9002

