apiVersion: apps/v1
kind: Deployment
metadata:
  name: tako-api
  namespace: app
  labels:
    app: tako-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: tako-api
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: tako-api
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9008"
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: node-type
                operator: In
                values: ["app"]
        # 优先打散到不同节点，避免单节点承载多个副本
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                topologyKey: kubernetes.io/hostname
                labelSelector:
                  matchLabels:
                    app: tako-api
      # 跨节点均衡：3 台节点之间的副本数差值不超过 1
      topologySpreadConstraints:
        - maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: DoNotSchedule
          labelSelector:
            matchLabels:
              app: tako-api
      hostAliases:
        - ip: "***********"
          hostnames: ["middleware-01"]
        - ip: "***********"
          hostnames: ["middleware-02"]
        - ip: "***********"
          hostnames: ["middleware-03"]
        - ip: "***********"
          hostnames: ["clickhouse"]
        - ip: "************"
          hostnames: ["mysql"]
      containers:
        - name: tako-api
          image: docker-registry.local:5000/tako-api:1.2.3
          ports:
            - name: http
              containerPort: 8008
            - name: metrics
              containerPort: 9008
          env:
            - name: SPRING_APPLICATION_JSON
              value: '{"spring.profiles.active":"prod"}'
            - name: TZ
              value: "Asia/Shanghai"
            - name: JAVA_TOOL_OPTIONS
              value: "-Duser.timezone=Asia/Shanghai"
            - name: BPL_JVM_HEAP_SIZE
              value: "4096M"
            - name: BPL_JVM_JVM_OPTIONS
              value: "-XX:+UseG1GC -XX:MaxMetaspaceSize=256M -Dfile.encoding=UTF-8"
          resources:
            requests:
              memory: "4Gi"
              cpu: "500m"
            limits:
              memory: "6Gi"
              cpu: "4"
          livenessProbe:
            exec:
              command: ["curl", "-f", "http://localhost:8008/Status/Version"]
            initialDelaySeconds: 40
            periodSeconds: 30 
            timeoutSeconds: 10
            failureThreshold: 3
          readinessProbe:
            exec:
              command: ["curl", "-f", "http://localhost:8008/Status/Version"]
            initialDelaySeconds: 40
            periodSeconds: 30
            timeoutSeconds: 10
            failureThreshold: 3
      restartPolicy: Always

---

apiVersion: v1
kind: Service
metadata:
  name: tako-api
  namespace: app
spec:
  type: ClusterIP
  selector:
    app: tako-api
  ports:
    - name: http
      protocol: TCP
      port: 8008
      targetPort: 8008
    - name: metrics
      protocol: TCP
      port: 9008
      targetPort: 9008
