apiVersion: apps/v1
kind: Deployment
metadata:
  name: mflow-in
  namespace: app
  labels:
    app: mflow-in
spec:
  replicas: 3
  selector:
    matchLabels:
      app: mflow-in
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: mflow-in
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9003"
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: node-type
                operator: In
                values: ["app"]
        # 优先跨节点打散
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                topologyKey: kubernetes.io/hostname
                labelSelector:
                  matchLabels:
                    app: mflow-in
      # 保证 3 台节点上的副本差值不超过 1
      topologySpreadConstraints:
        - maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: DoNotSchedule
          labelSelector:
            matchLabels:
              app: mflow-in
      hostAliases:
        - ip: "***********"
          hostnames: ["middleware-01"]
        - ip: "***********"
          hostnames: ["middleware-02"]
        - ip: "***********"
          hostnames: ["middleware-03"]
        - ip: "***********"
          hostnames: ["clickhouse"]
        - ip: "************"
          hostnames: ["mysql"]
      containers:
        - name: mflow-in
          image: docker-registry.local:5000/mflow-in:1.2.3
          ports:
            - name: http
              containerPort: 8003
            - name: metrics
              containerPort: 9003
          env:
            - name: SPRING_APPLICATION_JSON
              value: '{"spring.profiles.active":"prod"}'
            - name: TZ
              value: "Asia/Shanghai"
            - name: JAVA_TOOL_OPTIONS
              value: "-Duser.timezone=Asia/Shanghai"
            - name: BPL_JVM_HEAP_SIZE
              value: "4096M"  # 与 6Gi limit 配比约 66%
            - name: BPL_JVM_JVM_OPTIONS
              value: "-XX:+UseG1GC -XX:MaxMetaspaceSize=256M -Dfile.encoding=UTF-8"
          resources:
            requests:
              memory: "4Gi"
              cpu: "500m"
            limits:
              memory: "6Gi"
              cpu: "4"
          livenessProbe:
            exec:
              command: ["curl","-f","http://localhost:8003/Status/Version"]
            initialDelaySeconds: 40
            periodSeconds: 30
            timeoutSeconds: 10
            failureThreshold: 3
          readinessProbe:
            exec:
              command: ["curl","-f","http://localhost:8003/Status/Version"]
            initialDelaySeconds: 40
            periodSeconds: 30
            timeoutSeconds: 10
            failureThreshold: 3
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: mflow-in
  namespace: app
spec:
  type: ClusterIP
  selector:
    app: mflow-in
  ports:
    - name: http
      protocol: TCP
      port: 8003
      targetPort: 8003
    - name: metrics
      protocol: TCP
      port: 9003
      targetPort: 9003

