#!/bin/bash

echo "=== 方案2: 使用 NGINX 反向代理到 Traefik 80 端口 ==="
echo

# 创建 NGINX 配置
cat > /tmp/nginx-k3s-proxy.conf << 'EOF'
# K3S 应用服务代理配置
upstream traefik-backend {
    server 127.0.0.1:80;
}

# 应用服务端口映射
server {
    listen 8001;
    location / {
        proxy_pass http://traefik-backend;
        proxy_set_header Host principal-in.app.svc.cluster.local;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}

server {
    listen 8002;
    location / {
        proxy_pass http://traefik-backend;
        proxy_set_header Host tako-in.app.svc.cluster.local;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}

server {
    listen 8003;
    location / {
        proxy_pass http://traefik-backend;
        proxy_set_header Host mflow-in.app.svc.cluster.local;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}

server {
    listen 8004;
    location / {
        proxy_pass http://traefik-backend;
        proxy_set_header Host jingway-in.app.svc.cluster.local;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}

server {
    listen 8005;
    location / {
        proxy_pass http://traefik-backend;
        proxy_set_header Host statistics-in.app.svc.cluster.local;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}

server {
    listen 8006;
    location / {
        proxy_pass http://traefik-backend;
        proxy_set_header Host argus-api.app.svc.cluster.local;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}

server {
    listen 8007;
    location / {
        proxy_pass http://traefik-backend;
        proxy_set_header Host gateway-api.app.svc.cluster.local;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}

server {
    listen 8008;
    location / {
        proxy_pass http://traefik-backend;
        proxy_set_header Host tako-api.app.svc.cluster.local;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}

server {
    listen 8009;
    location / {
        proxy_pass http://traefik-backend;
        proxy_set_header Host jingway-api.app.svc.cluster.local;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}

server {
    listen 8013;
    location / {
        proxy_pass http://traefik-backend;
        proxy_set_header Host zeus.app.svc.cluster.local;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}

server {
    listen 8899;
    location / {
        proxy_pass http://traefik-backend;
        proxy_set_header Host elastic-job-console.app.svc.cluster.local;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}

# OPS 服务
server {
    listen 3000;
    location / {
        proxy_pass http://traefik-backend;
        proxy_set_header Host grafana.ops.svc.cluster.local;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}

server {
    listen 9090;
    location / {
        proxy_pass http://traefik-backend;
        proxy_set_header Host prometheus.ops.svc.cluster.local;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
EOF

echo "NGINX 配置文件已创建在 /tmp/nginx-k3s-proxy.conf"
echo
echo "请执行以下步骤安装和配置 NGINX："
echo "1. 安装 NGINX: yum install -y nginx 或 apt-get install -y nginx"
echo "2. 复制配置: cp /tmp/nginx-k3s-proxy.conf /etc/nginx/conf.d/"
echo "3. 测试配置: nginx -t"
echo "4. 启动服务: systemctl enable --now nginx"
echo "5. 检查状态: systemctl status nginx"
echo
echo "注意：这种方案需要修改 IngressRoute 使用基于 Host 的路由"
