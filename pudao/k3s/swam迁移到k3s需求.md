协助我在完全离线的网络环境中部署K3S替换掉老出网络故障的docker swarm,当下有4台机器,规划其中************ ************ ************ 搭建集群的控制面,主要用于部署app,************部署运维相关ops比如Prometheus Promtail Loki Grafana 。请细化部署流程，使用K3S版本 v1.33.3+k3s1

# 下载 k3s 安装二进制
https://github.com/k3s-io/k3s/releases/download/v1.33.3+k3s1/k3s
chmod +x k3s
# 下载 Airgap 镜像包（包含 k3s core 及所有组件镜像）
wget https://github.com/k3s-io/k3s/releases/download/v1.33.3+k3s1/k3s-airgap-images-amd64.tar.gz

当前swarm环境是为生产环境刚部署的还没正式上线，现在迁移到k3s可能是最合适的时机。当前的现状是在 ************ 上用纯docker compose部署了docker-registry registry:2.8.3，所有swarm用到的images目前都在docker-registry上，  然后还以swarm节点的身份部署了
docker-registry.local:5000/loki:3.5.1
docker-registry.local:5000/grafana:12.0.1-security-01
docker-registry.local:5000/prometheus:v3.4.1
************本来不想作为manager的，因为Prometheus自动发现容器实例IP拉取metrics需要权限才不得不提升为manager

以下三个节点是docker swarm manager，
************
************
************
用于部署应用，包含以下images
docker-registry.local:5000/tako-api:1.2.3
docker-registry.local:5000/jingway-api:1.2.3
docker-registry.local:5000/principal-in:1.2.3
docker-registry.local:5000/tako-in:1.2.3
docker-registry.local:5000/mflow-in:1.2.3
docker-registry.local:5000/jingway-in:1.2.3
docker-registry.local:5000/statistics-in:1.2.3
docker-registry.local:5000/argus-api:1.2.3
docker-registry.local:5000/gateway-api:1.2.3
docker-registry.local:5000/elastic-job-lite-console:latest
docker-registry.local:5000/zeus:1.2.0

应用把日志标准输出，会输出到宿主机，然后在这三台机器上部署Promtail用于采集标准输出日志发送到loki，Grafana用于展示loki日志和Prometheus监控。

总结：帮助做迁移选型，以及如何部署,当前的swarm集群肯定要解散掉，那docker应用需要卸载吗？请理解当前现状，然后协助我进行迁移

希望使用的 k3s 的 containerd 默认运行时，可以跟docker共存吗？
可以继续用现有的docker-registry作为镜像仓库，但是docker-registry不能自动清理陈旧的镜像，需要手动清理或者脚本清理，harbor是不是可以自动清理陈旧的镜像？代价是什么？资源占用高多少？
************ 部署ops应用  ************ ************ ************ 部署应用

生产环境是在完全离线的环境中，但是不需要平滑过渡，平滑过渡意味着复杂，现在可以一刀切。

我其实没用过k3s 但是有k8s的部署使用经验 希望你能站在我的角度先从大局规划，然后再给出实际可操纵的迁移步骤。附件供参考。

请协助我完成完整的迁移流程，包括但不限于app和ops部分的应用部署和配置、对外暴露服务，ops部分可以用helm部署但是得注意这是离线环境，一定要给出详细的操作流程

还有个细节要交代，因为这是完全离线的环境，在完全离线的云桌面通过服务商提供的端口映射访问环境内的服务，这也是为什么在swarm里用到了ports配置将端口映射到宿主机，不知道k3s如何优雅处理这个问题，要遵从最佳实践。


然后我按照 k3s/k3s.md 部署了k3s，app全都部署成功了，现在想将app服务暴露出来，有两个方案：
第一个方案是将app的端口直接映射到宿主机上，使用traefik/traefik-helmchartconfig.yaml 部署traefik，然后使用ingress/ingressroutes.yaml 部署ingress 但是没成功， 我还挪到了/var/lib/rancher/k3s/server/manifests/ 重启k3s都没成功， kubectl -n kube-system get svc traefik 仍然只监听 443 80 端口，不知道哪里没配置对

第二个方案我能想到的时直接用traefik的80端口进行代理，通过service name 访问具体的应用服务，这应该是业内比较通用的做法，缺点就是我需要在宿主机上安装一个NGINX用于将访问宿主机的端口反向代理svc域名访问traefik，如果第一个方案不可行就只有选择这个方案了