apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: principal-in-ext
  namespace: app
spec:
  entryPoints:
    - principal-in
  routes:
    - match: PathPrefix(`/`)
      kind: Rule
      services:
        - name: principal-in
          port: 8001
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: tako-in-ext
  namespace: app
spec:
  entryPoints:
    - tako-in
  routes:
    - match: PathPrefix(`/`)
      kind: Rule
      services:
        - name: tako-in
          port: 8002
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: mflow-in-ext
  namespace: app
spec:
  entryPoints:
    - mflow-in
  routes:
    - match: PathPrefix(`/`)
      kind: Rule
      services:
        - name: mflow-in
          port: 8003
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: jingway-in-ext
  namespace: app
spec:
  entryPoints:
    - jingway-in
  routes:
    - match: PathPrefix(`/`)
      kind: Rule
      services:
        - name: jingway-in
          port: 8004
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: statistics-in-ext
  namespace: app
spec:
  entryPoints:
    - statistics-in
  routes:
    - match: PathPrefix(`/`)
      kind: Rule
      services:
        - name: statistics-in
          port: 8005
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: argus-api-ext
  namespace: app
spec:
  entryPoints:
    - argus-api
  routes:
    - match: PathPrefix(`/`)
      kind: Rule
      services:
        - name: argus-api
          port: 8006
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: elastic-job-ext
  namespace: app
spec:
  entryPoints:
    - elastic-job-console
  routes:
    - match: PathPrefix(`/`)
      kind: Rule
      services:
        - name: elastic-job-console
          port: 8899
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: tako-api-ext
  namespace: app
spec:
  entryPoints:
    - tako-api
  routes:
    - match: PathPrefix(`/`)
      kind: Rule
      services:
        - name: tako-api
          port: 8008
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: jingway-api-ext
  namespace: app
spec:
  entryPoints:
    - jingway-api
  routes:
    - match: PathPrefix(`/`)
      kind: Rule
      services:
        - name: jingway-api
          port: 8009
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: gateway-api-ext
  namespace: app
spec:
  entryPoints:
    - gateway-api
  routes:
    - match: PathPrefix(`/`)
      kind: Rule
      services:
        - name: gateway-api
          port: 8007
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: zeus-ext
  namespace: app
spec:
  entryPoints:
    - zeus
  routes:
    - match: PathPrefix(`/`)
      kind: Rule
      services:
        - name: zeus
          port: 8013
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: grafana-ext
  namespace: ops
spec:
  entryPoints:
    - grafana
  routes:
    - match: PathPrefix(`/`)
      kind: Rule
      services:
        - name: grafana
          port: 3000
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: prometheus-ext
  namespace: ops
spec:
  entryPoints:
    - prometheus
  routes:
    - match: PathPrefix(`/`)
      kind: Rule
      services:
        - name: prometheus
          port: 9090