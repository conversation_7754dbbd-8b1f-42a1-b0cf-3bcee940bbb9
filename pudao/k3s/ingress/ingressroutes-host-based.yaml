# 基于 Host 的 IngressRoute 配置（用于方案2：NGINX反向代理）
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: principal-in-host
  namespace: app
spec:
  entryPoints:
    - web
  routes:
    - match: Host(`principal-in.app.svc.cluster.local`)
      kind: Rule
      services:
        - name: principal-in
          port: 8001
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: tako-in-host
  namespace: app
spec:
  entryPoints:
    - web
  routes:
    - match: Host(`tako-in.app.svc.cluster.local`)
      kind: Rule
      services:
        - name: tako-in
          port: 8002
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: mflow-in-host
  namespace: app
spec:
  entryPoints:
    - web
  routes:
    - match: Host(`mflow-in.app.svc.cluster.local`)
      kind: Rule
      services:
        - name: mflow-in
          port: 8003
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: jingway-in-host
  namespace: app
spec:
  entryPoints:
    - web
  routes:
    - match: Host(`jingway-in.app.svc.cluster.local`)
      kind: Rule
      services:
        - name: jingway-in
          port: 8004
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: statistics-in-host
  namespace: app
spec:
  entryPoints:
    - web
  routes:
    - match: Host(`statistics-in.app.svc.cluster.local`)
      kind: Rule
      services:
        - name: statistics-in
          port: 8005
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: argus-api-host
  namespace: app
spec:
  entryPoints:
    - web
  routes:
    - match: Host(`argus-api.app.svc.cluster.local`)
      kind: Rule
      services:
        - name: argus-api
          port: 8006
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: gateway-api-host
  namespace: app
spec:
  entryPoints:
    - web
  routes:
    - match: Host(`gateway-api.app.svc.cluster.local`)
      kind: Rule
      services:
        - name: gateway-api
          port: 8007
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: tako-api-host
  namespace: app
spec:
  entryPoints:
    - web
  routes:
    - match: Host(`tako-api.app.svc.cluster.local`)
      kind: Rule
      services:
        - name: tako-api
          port: 8008
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: jingway-api-host
  namespace: app
spec:
  entryPoints:
    - web
  routes:
    - match: Host(`jingway-api.app.svc.cluster.local`)
      kind: Rule
      services:
        - name: jingway-api
          port: 8009
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: zeus-host
  namespace: app
spec:
  entryPoints:
    - web
  routes:
    - match: Host(`zeus.app.svc.cluster.local`)
      kind: Rule
      services:
        - name: zeus
          port: 8013
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: elastic-job-console-host
  namespace: app
spec:
  entryPoints:
    - web
  routes:
    - match: Host(`elastic-job-console.app.svc.cluster.local`)
      kind: Rule
      services:
        - name: elastic-job-console
          port: 8899
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: grafana-host
  namespace: ops
spec:
  entryPoints:
    - web
  routes:
    - match: Host(`grafana.ops.svc.cluster.local`)
      kind: Rule
      services:
        - name: grafana
          port: 3000
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: prometheus-host
  namespace: ops
spec:
  entryPoints:
    - web
  routes:
    - match: Host(`prometheus.ops.svc.cluster.local`)
      kind: Rule
      services:
        - name: prometheus
          port: 9090
