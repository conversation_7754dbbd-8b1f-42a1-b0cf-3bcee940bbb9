// 🎯 最优雅版本 - 基于您的代码优化
def processMapping(context) {
    def mapping = [
        EMP09S968: 'oc101', EMP08S968: 'oc102', EMP01S968: 'oc103', EMP04S968: 'oc104',
        EMP02S968: 'oc105', EMP07S968: 'oc106', EMP10S968: 'oc107'
    ]
    
    def winner = mapping.findAll { key, _ -> isValid(context[key]) }
                       .max { key, _ -> context[key] as Integer }
    
    return winner ? [JOBPRO01: winner.value, flag: 1] : [JOBPRO01: '-1', flag: 0]
}

// 🛠️ 工具方法
def isValid(value) {
    value != null && value != '' && value != -1 && value != '-1'
}

// 📋 备选方案1：保持您原有的代码结构，但简化
def processMappingLikeYours(context) {
    def mapping = [
        EMP09S968: 'oc101', EMP08S968: 'oc102', EMP01S968: 'oc103', EMP04S968: 'oc104',
        EMP02S968: 'oc105', EMP07S968: 'oc106', EMP10S968: 'oc107'
    ]

    def validValues = mapping.collect { key, oc -> 
        [value: context[key], key: key, oc: oc]
    }.findAll { item -> isValid(item.value) }

    if (!validValues) {
        return [JOBPRO01: '-1', flag: 0]
    }

    def maxItem = validValues.max { it.value as Integer }
    return [JOBPRO01: maxItem.oc, flag: 1]
}

// ⚡ 备选方案2：超简洁版本（一行搞定）
def processMappingOneLiner(context) {
    [EMP09S968: 'oc101', EMP08S968: 'oc102', EMP01S968: 'oc103', EMP04S968: 'oc104', EMP02S968: 'oc105', EMP07S968: 'oc106', EMP10S968: 'oc107']
        .findAll { k, v -> isValid(context[k]) }
        .max { k, v -> context[k] as Integer }
        ?.with { k, v -> [JOBPRO01: v, flag: 1] } ?: [JOBPRO01: '-1', flag: 0]
}

// 🎨 备选方案3：如果您想要更清晰的可读性
def processMappingReadable(context) {
    // 定义映射关系
    def empToOc = [
        EMP09S968: 'oc101', EMP08S968: 'oc102', EMP01S968: 'oc103', EMP04S968: 'oc104',
        EMP02S968: 'oc105', EMP07S968: 'oc106', EMP10S968: 'oc107'
    ]
    
    // 筛选有效的条目
    def validEntries = empToOc.findAll { empKey, ocValue -> 
        isValid(context[empKey])
    }
    
    // 如果没有有效值，返回默认结果
    if (!validEntries) {
        return [JOBPRO01: '-1', flag: 0]
    }
    
    // 找到值最大的条目
    def maxEntry = validEntries.max { empKey, ocValue -> 
        context[empKey] as Integer
    }
    
    // 返回结果
    return [JOBPRO01: maxEntry.value, flag: 1]
}

// 🔧 如果您需要在实际项目中使用，推荐这个版本（最稳定）
def processMappingProduction(context) {
    def mapping = [
        'EMP09S968': 'oc101', 'EMP08S968': 'oc102', 'EMP01S968': 'oc103', 'EMP04S968': 'oc104',
        'EMP02S968': 'oc105', 'EMP07S968': 'oc106', 'EMP10S968': 'oc107'
    ]

    def validItems = []
    mapping.each { key, oc ->
        def value = context?."$key"
        if (isValid(value)) {
            validItems << [value: value as Integer, oc: oc, key: key]
        }
    }

    if (!validItems) {
        return [JOBPRO01: '-1', flag: 0]
    }

    def maxItem = validItems.max { it.value }
    return [JOBPRO01: maxItem.oc, flag: 1]
}

// 测试数据
def testData = [
    normal: [EMP09S968: 10, EMP08S968: 5, EMP01S968: 15, EMP04S968: 8, EMP02S968: 20, EMP07S968: 3, EMP10S968: 12],
    empty: [EMP09S968: null, EMP08S968: -1, EMP01S968: '', EMP04S968: -1, EMP02S968: null, EMP07S968: '', EMP10S968: -1],
    duplicate: [EMP09S968: 20, EMP08S968: 15, EMP01S968: 20, EMP04S968: 10, EMP02S968: 18, EMP07S968: 20, EMP10S968: 12]
]

testData.each { scenario, context ->
    println "\n=== ${scenario.toUpperCase()} 场景测试 ==="
    println "主版本:      ${processMapping(context)}"
    println "类似您的版本: ${processMappingLikeYours(context)}"
    println "超简洁版本:   ${processMappingOneLiner(context)}"
    println "可读性版本:   ${processMappingReadable(context)}"
    println "生产环境版本: ${processMappingProduction(context)}"
}

println "\n" + "="*60
println "💡 推荐使用："
println "1. 如果追求简洁：processMappingOneLiner"
println "2. 如果追求稳定：processMappingProduction" 
println "3. 如果在您现有基础上改进：processMappingLikeYours" 