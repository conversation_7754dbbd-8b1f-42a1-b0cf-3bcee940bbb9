
```sh
cd ../principal && ./gradlew :principal-in:bootBuildImage -x test

# cd ../tako && ./gradlew :tako-api:bootBuildImage -x test
# cd ../tako && ./gradlew :tako-in:bootBuildImage -x test
cd ../tako && ./gradlew :tako-api:bootBuildImage :tako-in:bootBuildImage -x test

cd ../mflow && ./gradlew :mflow-in:bootBuildImage -x test

# cd ../jingway && ./gradlew :jingway-in:bootBuildImage -x test
# cd ../jingway && ./gradlew :jingway-api:bootBuildImage -x test
cd ../jingway && ./gradlew :jingway-in:bootBuildImage :jingway-api:bootBuildImage -x test

cd ../statistics && ./gradlew :statistics-in:bootBuildImage -x test
cd ../argus && ./gradlew :argus-api:bootBuildImage -x test
cd ../decision-gateway && ./gradlew :gateway-api:bootBuildImage -x test


# 批量保存镜像
docker images --format "{{.Repository}}:{{.Tag}}" | grep "^docker-registry.local:5000" | while read image; do filename=$(echo "$image" | sed 's|docker-registry.local:5000/||' | sed 's|:|_|' | sed 's|/|_|g').tar; echo "Saving $image -> $filename"; docker save "$image" -o "$filename"; done

# 批量加载镜像
find . -name "*.tar" -type f | while read tar_file; do echo "Loading $(basename "$tar_file")"; docker load -i "$tar_file"; done

# 批量推送镜像
docker images --format "{{.Repository}}:{{.Tag}}" | grep "1.2.3" | while read image; do echo "Pushing $image"; docker push "docker-registry.local:5000/$image"; done


docker save docker-registry.local:5000/principal-in:1.2.3
docker save docker-registry.local:5000/tako-in:1.2.3
docker save docker-registry.local:5000/mflow-in:1.2.3
docker save docker-registry.local:5000/jingway-in:1.2.3
docker save docker-registry.local:5000/statistics-in:1.2.3
docker save docker-registry.local:5000/argus-api:1.2.3
docker save docker-registry.local:5000/gateway-api:1.2.3
docker save docker-registry.local:5000/tako-api:1.2.3
docker save docker-registry.local:5000/jingway-api:1.2.3


docker push docker-registry.local:5000/principal-in:1.2.3
docker push docker-registry.local:5000/tako-in:1.2.3
docker push docker-registry.local:5000/mflow-in:1.2.3
docker push docker-registry.local:5000/jingway-in:1.2.3
docker push docker-registry.local:5000/statistics-in:1.2.3
docker push docker-registry.local:5000/argus-api:1.2.3
docker push docker-registry.local:5000/gateway-api:1.2.3
docker push docker-registry.local:5000/tako-api:1.2.3
docker push docker-registry.local:5000/jingway-api:1.2.3
