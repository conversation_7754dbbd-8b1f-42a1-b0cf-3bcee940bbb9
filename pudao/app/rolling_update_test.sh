#!/bin/bash
#
# Docker Swarm 滚动更新压力测试脚本 (生产级优化版)
#
# 功能:
#   - 模拟生产环境标准发布流程，进行高强度的滚动更新。
#   - 将所有操作和检查的详细日志输出到文件，便于后台运行和事后分析。
#   - 在每次更新后，检查 Docker 守护进程日志，以捕获底层错误。
#   - 增加 `docker service ls` 检查，提供更全面的状态视图。
#

# -------- 请在这里配置您的测试参数 --------

# 您的堆栈（Stack）名称
STACK_NAME="app"

# 您的 Compose 文件名
COMPOSE_FILE="docker-stack.yml"

# 您想要进行滚动更新测试的服务名称
# (必须是您 docker-stack.yml 文件中定义的一个 service)
SERVICE_TO_UPDATE="principal-in"

# 准备用于来回切换的两个镜像版本标签
IMAGE_BASE_NAME="docker-registry.local:5000/principal-in" # 镜像的基础名称
IMAGE_TAG_A="1.1.0" # 版本 A
IMAGE_TAG_B="1.0.4" # 版本 B

# 您想执行的测试循环次数
# (一次循环包含一次 A->B 和一次 B->A 的更新)
ITERATIONS=1000

# 每次部署操作后等待的时间（秒）
SLEEP_DURATION=10

# -------- 新增: 日志文件配置 --------
LOG_FILE="./swarm_update_test_$(date +%Y%m%d_%H%M%S).log"

# -------- 脚本颜色定义 --------
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# -------- 脚本主逻辑 --------

# 【关键优化】将所有标准输出和错误输出都重定向到日志文件
# 同时，使用 tee 将其也打印到控制台，方便实时查看
exec > >(tee -a "$LOG_FILE") 2>&1

# 检查文件和命令是否存在
if [ ! -f "$COMPOSE_FILE" ]; then
    echo -e "${RED}错误: '$COMPOSE_FILE' 文件未找到！${NC}"
    exit 1
fi
if ! command -v sed &> /dev/null; then
    echo -e "${RED}错误: 'sed' 命令未找到，无法修改配置文件。${NC}"
    exit 1
fi

echo -e "${GREEN}准备开始对服务 '${SERVICE_TO_UPDATE}' 进行 ${ITERATIONS} 次滚动更新测试...${NC}"
echo -e "${GREEN}所有日志将被记录到: ${LOG_FILE}${NC}"


# 【新增】函数：检查 Docker 守护进程日志中的错误
check_docker_errors() {
    echo -e "\n${CYAN}--- 正在检查近 1 分钟内的 Docker 守护进程错误... ---${NC}"
    # 使用 journalctl 查找最近的错误或警告日志
    # --no-pager 确保命令不会卡在翻页器中
    if journalctl -u docker --since "1 minute ago" --no-pager | grep -i -E "error|fail|warn|denied"; then
        echo -e "${YELLOW}警告: 在 Docker 日志中检测到潜在问题！详情请查看 journalctl。${NC}"
    else
        echo -e "${GREEN}INFO: Docker 守护进程日志在近 1 分钟内无明显错误。${NC}"
    fi
    echo -e "${CYAN}------------------------------------------------------${NC}"
}


# 函数：用于更新镜像版本并部署
perform_update() {
    local service_name=$1
    local new_tag=$2
    local old_tag=$3

    echo -e "\n${CYAN}>>> 准备将服务 [${service_name}] 的镜像版本从 ${old_tag} 更新到 ${new_tag}...${NC}"

    # 步骤 1: 使用 sed 自动修改配置文件
    echo "--> [1/4] 正在修改 '$COMPOSE_FILE' 文件..."
    sed -i "s|${IMAGE_BASE_NAME}:${old_tag}|${IMAGE_BASE_NAME}:${new_tag}|g" "$COMPOSE_FILE"
    echo "--> 修改完成。"

    # 步骤 2: 部署更新
    echo "--> [2/4] 执行 'docker stack deploy' 触发滚动更新..."
    docker stack deploy -c "$COMPOSE_FILE" "$STACK_NAME"
    echo "--> 更新命令已发送。"

    # 【优化】检查部署后的 Docker 错误
    echo "--> [2/4] 等待 5 秒后检查 Docker 守护进程日志..."
    sleep 5
    check_docker_errors

    # 步骤 3: 监控更新过程
    echo "--> [3/4] 等待 ${SLEEP_DURATION} 秒，开始监控更新状态..."
    sleep $SLEEP_DURATION
    
    while true; do
        echo -e "\n--- 当前服务概览 (时间: $(date +%T)) ---"
        # 【新增】列出所有服务的状态
        docker service ls --filter name="${STACK_NAME}_"
        
        echo -e "\n--- [${service_name}] 副本状态 ---"
        docker service ps "${STACK_NAME}_${service_name}" --no-trunc
        
        running_new_replicas=$(docker service ps "${STACK_NAME}_${service_name}" --filter "desired-state=running" -q | xargs docker inspect --format '{{.Status.ContainerStatus.ContainerID}} {{.Spec.ContainerSpec.Image}}' 2>/dev/null | grep "${IMAGE_BASE_NAME}:${new_tag}" | wc -l)
        total_replicas=$(docker service ls --filter name="${STACK_NAME}_${service_name}" --format "{{.Replicas}}" | cut -d'/' -f2)

        if [ "$running_new_replicas" -eq "$total_replicas" ]; then
            echo -e "${GREEN}--> [成功] 所有 ${total_replicas} 个副本已成功更新到版本 ${new_tag}！${NC}"
            break
        else
            echo "--> 正在更新中... (${running_new_replicas}/${total_replicas} 个新副本已就绪)。将在 ${SLEEP_DURATION} 秒后再次检查。"
            sleep $SLEEP_DURATION
        fi
    done

    # 【新增】更新完成后的 Docker 错误检查
    echo "--> [4/4] 更新完成，再次检查 Docker 守护进程日志..."
    check_docker_errors
}


# 主循环
for i in $(seq 1 $ITERATIONS)
do
    echo -e "\n${YELLOW}=======================================================${NC}"
    echo -e "${YELLOW}  开始第 $i / $ITERATIONS 次测试循环...${NC}"
    echo -e "${YELLOW}=======================================================${NC}"

    # 从版本 A 更新到 B
    perform_update "$SERVICE_TO_UPDATE" "$IMAGE_TAG_B" "$IMAGE_TAG_A"

    echo -e "\n${CYAN}--- 等待 30 秒进行下一次反向更新 ---${NC}"
    sleep 30

    # 从版本 B 更新回 A
    perform_update "$SERVICE_TO_UPDATE" "$IMAGE_TAG_A" "$IMAGE_TAG_B"
done

echo -e "\n${GREEN}===============================================${NC}"
echo -e "${GREEN}  所有滚动更新测试循环已成功完成！${NC}"
echo -e "${GREEN}===============================================${NC}"
