#!/bin/bash
#
# Docker Swarm 压力测试脚本
#
# 功能:
#   重复执行 `docker stack deploy` 和 `docker stack rm` 命令，
#   用于测试 Swarm 集群在频繁部署和删除操作下的稳定性，
#   特别适合用于复现与 VIP 分配相关的底层 Bug。
#

# -------- 可配置变量 --------

# 您的堆栈（Stack）名称
STACK_NAME="app"

# 您的 Compose 文件名
COMPOSE_FILE="docker-stack.yml"

# 您想执行的测试循环次数
ITERATIONS=100

# 每次部署或删除后等待的时间（秒），给 Swarm 足够的时间来完成操作
SLEEP_DURATION=30

# -------- 脚本颜色定义 --------
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# -------- 脚本主逻辑 --------

# 检查 docker-stack.yml 文件是否存在
if [ ! -f "$COMPOSE_FILE" ]; then
    echo -e "${RED}错误: '$COMPOSE_FILE' 文件未找到！${NC}"
    echo -e "${RED}请将此脚本与您的 docker-stack.yml 文件放在同一个目录下。${NC}"
    exit 1
fi

echo -e "${GREEN}准备开始对堆栈 '${STACK_NAME}' 进行 ${ITERATIONS} 次部署/删除压力测试...${NC}"

# 主循环
for i in $(seq 1 $ITERATIONS)
do
    echo -e "\n${YELLOW}===============================================${NC}"
    echo -e "${YELLOW}  开始第 $i / $ITERATIONS 次压力测试循环...${NC}"
    echo -e "${YELLOW}===============================================${NC}"

    # 步骤 1: 部署堆栈
    echo -e "--> [步骤 1/4] 正在部署堆栈 '${STACK_NAME}'..."
    docker stack deploy -c "$COMPOSE_FILE" "$STACK_NAME"
    if [ $? -ne 0 ]; then
        echo -e "${RED}错误: 堆栈部署失败！正在中止测试。${NC}"
        exit 1
    fi
    echo -e "--> [步骤 1/4] 部署命令已发送。"

    # 步骤 2: 等待并检查部署状态
    echo -e "--> [步骤 2/4] 等待 ${SLEEP_DURATION} 秒让服务稳定..."
    sleep $SLEEP_DURATION
    echo -e "--> [步骤 2/4] 检查服务状态:"
    docker service ls --filter name="${STACK_NAME}_" || echo -e "${YELLOW}警告: 无法获取服务状态，可能仍在部署中。${NC}"


    # 步骤 3: 移除堆栈
    echo -e "--> [步骤 3/4] 正在移除堆栈 '${STACK_NAME}'..."
    docker stack rm "$STACK_NAME"
    if [ $? -ne 0 ]; then
        echo -e "${RED}错误: 堆栈移除失败！请手动检查。正在中止测试。${NC}"
        exit 1
    fi
    echo -e "--> [步骤 3/4] 堆栈移除命令已发送。"

    # 步骤 4: 等待资源清理
    echo -e "--> [步骤 4/4] 等待 ${SLEEP_DURATION} 秒让资源被清理..."
    sleep $SLEEP_DURATION
    echo -e "${GREEN}--> 第 $i 次循环完成。${NC}"
done

echo -e "\n${GREEN}===============================================${NC}"
echo -e "${GREEN}  所有 ${ITERATIONS} 次测试循环已成功完成！${NC}"
echo -e "${GREEN}===============================================${NC}"
