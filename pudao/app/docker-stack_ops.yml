
services:

  loki:
    image: docker-registry.local:5000/loki:3.5.1
    command: -config.file=/etc/loki/local-config.yaml
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.labels.ops == true
      restart_policy:
        condition: on-failure
      labels:
        - "prometheus.io.scrape=true"
        - "prometheus.io.port=3100"
    networks:
      - shared-net
    ports: 
      - "3100:3100"
    volumes:
      - /data/loki/loki-config.yaml:/etc/loki/local-config.yaml
      - /data/loki/data:/loki


  grafana:
    image: docker-registry.local:5000/grafana:12.0.1-security-01
    volumes:
      - /data/grafana/data:/var/lib/grafana
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.labels.ops == true
      restart_policy:
        condition: on-failure
      labels:
        - "prometheus.io.scrape=true"
        - "prometheus.io.port=3000"
    networks:
      - shared-net
    ports: 
      - "3000:3000"

  prometheus:
    image: docker-registry.local:5000/prometheus:v3.4.1
    user: "0:0"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - /data/prometheus/config/prometheus.yml:/etc/prometheus/prometheus.yml
      - /data/prometheus/config/targets:/etc/prometheus/targets
      - /data/prometheus/data:/prometheus
    command:
      - "--config.file=/etc/prometheus/prometheus.yml"
      - "--storage.tsdb.path=/prometheus"
      - "--storage.tsdb.retention.time=15d"
      - "--web.enable-lifecycle"
    deploy:
      mode: replicated
      replicas: 1
      placement:
        constraints:
          - node.labels.ops == true
      restart_policy:
        condition: on-failure
      labels:
        - "prometheus.io.scrape=true"
        - "prometheus.io.port=9090"
    networks:
      - shared-net
    ports: 
      - "9090:9090"


networks:
  shared-net:
    external: true
