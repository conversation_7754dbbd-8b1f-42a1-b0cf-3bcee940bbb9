## app

```sh
mkdir /opt/app/principal-in          ## 8001 9001
mkdir /opt/app/tako-in               ## 8002 9002
mkdir /opt/app/mflow-in              ## 8003 9003
mkdir /opt/app/jingway-in            ## 8004 9094
mkdir /opt/app/statistics-in         ## 8005 9095
mkdir /opt/app/argus-api             ## 8006 9006
mkdir /opt/app/gateway-api           ## 8007 9007

mkdir /opt/app/tako-api              ## 8008 9008
mkdir /opt/app/jingway-api           ## 8009 9099

mkdir /opt/app/warpdrive-in          ## 8010 9010
mkdir /opt/app/dnginj-ml             # 8011 9011
mkdir /opt/app/modelengine           ## 8012 9012
mkdir /opt/app/zeus                  ## 8013 9013
```

## 编译Docker镜像

```sh
./gradlew clean :principal-in:bootBuildImage -x test
docker save -o principal-in.tar principal-in:1.0.0
docker load -i principal-in.tar
docker tag principal-in:1.0.0 docker-registry.local:5000/principal-in:1.0.0
docker push docker-registry.local:5000/principal-in:1.0.0

docker pull docker-registry.local:5000/principal-in:1.0.0

vim /data/stack/docker-stack.yml

docker node ls

docker stack deploy -c docker-stack.yml app --detach=false
# 删除 stack
docker stack rm app

# 查看聚合所有副本的日志
docker service logs app_principal-in

docker service ls
docker service ps app_principal-in

docker stack ps app

# 服务扩缩容
docker service scale app_principal-in=6

# 强制重启服务
docker service update --force app_principal-in

# 服务更新（滚动更新）
docker stack deploy -c docker-stack.yml app
docker service update --image docker-registry.local:5000/principal-in:1.0.1 app_principal-in

```
