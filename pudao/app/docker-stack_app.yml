x-common-hosts: &common-hosts
  extra_hosts:
    - "middleware-01:***********"
    - "middleware-02:***********"
    - "middleware-03:***********"
    - "clickhouse:***********"
    - "mysql:************"

services:
  tako-api:
    image: docker-registry.local:5000/tako-api:1.2.3
    <<: *common-hosts
    environment:
      - SPRING_APPLICATION_JSON={"spring.profiles.active":"prod"}
      - TZ=Asia/Shanghai
      - JAVA_TOOL_OPTIONS=-Duser.timezone=Asia/Shanghai
      - BPL_JVM_HEAP_SIZE=4096M
      - BPL_JVM_JVM_OPTIONS=-XX:+UseG1GC -XX:MaxMetaspaceSize=256M -Dfile.encoding=UTF-8
    deploy:
      replicas: 3
      update_config:
        parallelism: 1
        delay: 10s
      restart_policy:
        condition: on-failure
      placement:
        constraints:
          - node.labels.app == true
      labels:
        - "prometheus.io.scrape=true"
        - "prometheus.io.port=9008"
    networks:
      - shared-net
    ports:
      - 8008:8008
      - 9008:9008
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8008/Status/Version"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "300m"
        max-file: "10"


  jingway-api:
    image: docker-registry.local:5000/jingway-api:1.2.3
    <<: *common-hosts
    environment:
      - SPRING_APPLICATION_JSON={"spring.profiles.active":"prod"}
      - TZ=Asia/Shanghai
      - JAVA_TOOL_OPTIONS=-Duser.timezone=Asia/Shanghai
      - BPL_JVM_HEAP_SIZE=4096M
      - BPL_JVM_JVM_OPTIONS=-XX:+UseG1GC -XX:MaxMetaspaceSize=256M -Dfile.encoding=UTF-8
    deploy:
      replicas: 3
      update_config:
        parallelism: 1
        delay: 10s
      restart_policy:
        condition: on-failure
      placement:
        constraints:
          - node.labels.app == true
      labels:
        - "prometheus.io.scrape=true"
        - "prometheus.io.port=9099"
    networks:
      - shared-net
    ports:
      - 8009:8009
      - 9099:9099
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8009/Status/Version"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "300m"
        max-file: "10"

  principal-in:
    image: docker-registry.local:5000/principal-in:1.2.3
    <<: *common-hosts
    environment:
      - SPRING_APPLICATION_JSON={"spring.profiles.active":"prod"}
      - TZ=Asia/Shanghai
      - JAVA_TOOL_OPTIONS=-Duser.timezone=Asia/Shanghai
      - BPL_JVM_HEAP_SIZE=2048M
      - BPL_JVM_JVM_OPTIONS=-XX:+UseG1GC -XX:MaxMetaspaceSize=256M -Dfile.encoding=UTF-8
    deploy:
      replicas: 2
      update_config:
        parallelism: 1
        delay: 10s
      restart_policy:
        condition: on-failure
      placement:
        constraints:
          - node.labels.app == true
      labels:
        - "prometheus.io.scrape=true"
        - "prometheus.io.port=9001"
    networks:
      - shared-net
    ports:
      - 8001:8001
      - 9001:9001
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/Status/Version"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "300m"
        max-file: "10"

  tako-in:
    image: docker-registry.local:5000/tako-in:1.2.3
    <<: *common-hosts
    environment:
      - SPRING_APPLICATION_JSON={"spring.profiles.active":"prod"}
      - TZ=Asia/Shanghai
      - JAVA_TOOL_OPTIONS=-Duser.timezone=Asia/Shanghai
      - BPL_JVM_HEAP_SIZE=2048M
      - BPL_JVM_JVM_OPTIONS=-XX:+UseG1GC -XX:MaxMetaspaceSize=256M -Dfile.encoding=UTF-8
    deploy:
      replicas: 2
      update_config:
        parallelism: 1
        delay: 10s
      restart_policy:
        condition: on-failure
      placement:
        constraints:
          - node.labels.app == true
      labels:
        - "prometheus.io.scrape=true"
        - "prometheus.io.port=9002"
    networks:
      - shared-net
    ports:
      - 8002:8002
      - 9002:9002
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8002/Status/Version"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "300m"
        max-file: "10"

  mflow-in:
    image: docker-registry.local:5000/mflow-in:1.2.3
    <<: *common-hosts
    environment:
      - SPRING_APPLICATION_JSON={"spring.profiles.active":"prod"}
      - TZ=Asia/Shanghai
      - JAVA_TOOL_OPTIONS=-Duser.timezone=Asia/Shanghai
      - BPL_JVM_HEAP_SIZE=4096M
      - BPL_JVM_JVM_OPTIONS=-XX:+UseG1GC -XX:MaxMetaspaceSize=256M -Dfile.encoding=UTF-8
    deploy:
      replicas: 3
      update_config:
        parallelism: 1
        delay: 10s
      restart_policy:
        condition: on-failure
      placement:
        constraints:
          - node.labels.app == true
      labels:
        - "prometheus.io.scrape=true"
        - "prometheus.io.port=9003"
    networks:
      - shared-net
    ports:
      - 8003:8003
      - 9003:9003
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8003/Status/Version"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "300m"
        max-file: "10"

  jingway-in:
    image: docker-registry.local:5000/jingway-in:1.2.3
    <<: *common-hosts
    environment:
      - SPRING_APPLICATION_JSON={"spring.profiles.active":"prod"}
      - TZ=Asia/Shanghai
      - JAVA_TOOL_OPTIONS=-Duser.timezone=Asia/Shanghai
      - BPL_JVM_HEAP_SIZE=2048M
      - BPL_JVM_JVM_OPTIONS=-XX:+UseG1GC -XX:MaxMetaspaceSize=256M -Dfile.encoding=UTF-8
    deploy:
      replicas: 2
      update_config:
        parallelism: 1
        delay: 10s
      restart_policy:
        condition: on-failure
      placement:
        constraints:
          - node.labels.app == true
      labels:
        - "prometheus.io.scrape=true"
        - "prometheus.io.port=9094"
    networks:
      - shared-net
    ports:
      - 8004:8004
      - 9094:9094
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8004/Status/Version"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "300m"
        max-file: "10"

  statistics-in:
    image: docker-registry.local:5000/statistics-in:1.2.3
    <<: *common-hosts
    environment:
      - SPRING_APPLICATION_JSON={"spring.profiles.active":"prod"}
      - TZ=Asia/Shanghai
      - JAVA_TOOL_OPTIONS=-Duser.timezone=Asia/Shanghai
      - BPL_JVM_HEAP_SIZE=2048M
      - BPL_JVM_JVM_OPTIONS=-XX:+UseG1GC -XX:MaxMetaspaceSize=256M -Dfile.encoding=UTF-8
    deploy:
      replicas: 2
      update_config:
        parallelism: 1
        delay: 10s
      restart_policy:
        condition: on-failure
      placement:
        constraints:
          - node.labels.app == true
      labels:
        - "prometheus.io.scrape=true"
        - "prometheus.io.port=9095"
    networks:
      - shared-net
    ports:
      - 8005:8005
      - 9095:9095
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8005/Status/Version"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "300m"
        max-file: "10"

  argus-api:
    image: docker-registry.local:5000/argus-api:1.2.3
    <<: *common-hosts
    environment:
      - SPRING_APPLICATION_JSON={"spring.profiles.active":"prod"}
      - TZ=Asia/Shanghai
      - JAVA_TOOL_OPTIONS=-Duser.timezone=Asia/Shanghai
      - BPL_JVM_HEAP_SIZE=2048M
      - BPL_JVM_JVM_OPTIONS=-XX:+UseG1GC -XX:MaxMetaspaceSize=256M -Dfile.encoding=UTF-8
    deploy:
      replicas: 2
      update_config:
        parallelism: 1
        delay: 10s
      restart_policy:
        condition: on-failure
      placement:
        constraints:
          - node.labels.app == true
      labels:
        - "prometheus.io.scrape=true"
        - "prometheus.io.port=9006"
    networks:
      - shared-net
    ports:
      - 8006:8006
      - 9006:9006
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8006/Status/Version"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "300m"
        max-file: "10"

  gateway-api:
    image: docker-registry.local:5000/gateway-api:1.2.3
    <<: *common-hosts
    environment:
      - SPRING_APPLICATION_JSON={"spring.profiles.active":"prod"}
      - TZ=Asia/Shanghai
      - JAVA_TOOL_OPTIONS=-Duser.timezone=Asia/Shanghai
      - BPL_JVM_HEAP_SIZE=2048M
      - BPL_JVM_JVM_OPTIONS=-XX:+UseG1GC -XX:MaxMetaspaceSize=256M -Dfile.encoding=UTF-8
    deploy:
      replicas: 2
      update_config:
        parallelism: 1
        delay: 10s
      restart_policy:
        condition: on-failure
      placement:
        constraints:
          - node.labels.app == true
      labels:
        - "prometheus.io.scrape=true"
        - "prometheus.io.port=9007"
    networks:
      - shared-net
    ports:
      - 8007:8007
      - 9007:9007
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8007/Status/Version"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "300m"
        max-file: "10"

  elastic-job-console:
    image: docker-registry.local:5000/elastic-job-lite-console:latest
    <<: *common-hosts
    environment:
      ZK_CONNECTION_STRING: "middleware-01:2181,middleware-02:2181,middleware-03:2181"
      ELASTIC_ROOT_USERNAME: root
      ELASTIC_ROOT_PASSWORD: root
    deploy:
      replicas: 1
      update_config:
        parallelism: 1
        delay: 10s
      restart_policy:
        condition: on-failure
      placement:
        constraints:
          - node.labels.app == true
    networks:
      - shared-net
    ports:
      - "8899:8899"
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "3"

  zeus:
    image: docker-registry.local:5000/zeus:1.2.0
    deploy:
      replicas: 2
      update_config:
        parallelism: 1
        delay: 10s
      restart_policy:
        condition: on-failure
      placement:
        constraints:
          - node.labels.app == true
    networks:
      - shared-net
    ports:
      - 8013:8013
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "3"

networks:
  shared-net:
    external: true
