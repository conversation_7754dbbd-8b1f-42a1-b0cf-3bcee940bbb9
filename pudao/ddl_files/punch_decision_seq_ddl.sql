-- MySQL dump 10.13  Distrib 5.7.44, for Linux (x86_64)
--
-- Host: **********    Database: punch_decision_seq
-- ------------------------------------------------------
-- Server version	5.7.36-txsql-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Current Database: `punch_decision_seq`
--

CREATE DATABASE /*!32312 IF NOT EXISTS*/ `punch_decision_seq` /*!40100 DEFAULT CHARACTER SET utf8mb4 */;

USE `punch_decision_seq`;

--
-- Table structure for table `sequence_argus`
--

DROP TABLE IF EXISTS `sequence_argus`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sequence_argus` (
  `SequenceID` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `SequenceName` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'sequence名称',
  `SequenceValue` bigint(20) NOT NULL COMMENT 'sequence值',
  PRIMARY KEY (`SequenceID`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ID生成';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sequence_decision`
--

DROP TABLE IF EXISTS `sequence_decision`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sequence_decision` (
  `SequenceID` int(11) NOT NULL COMMENT '序列ID',
  `SequenceName` varchar(50) NOT NULL COMMENT '序列名称',
  `SequenceValue` bigint(20) NOT NULL COMMENT '序列值',
  PRIMARY KEY (`SequenceID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='决策工具发号器';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sequence_jingway`
--

DROP TABLE IF EXISTS `sequence_jingway`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sequence_jingway` (
  `SequenceID` int(11) NOT NULL COMMENT '序列ID',
  `SequenceName` varchar(50) NOT NULL COMMENT '序列名称',
  `SequenceValue` bigint(20) NOT NULL COMMENT '序列值',
  PRIMARY KEY (`SequenceID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='统计发号器';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sequence_statistics`
--

DROP TABLE IF EXISTS `sequence_statistics`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sequence_statistics` (
  `SequenceID` int(11) NOT NULL COMMENT '序列ID',
  `SequenceName` varchar(50) NOT NULL COMMENT '序列名称',
  `SequenceValue` bigint(20) NOT NULL COMMENT '序列值',
  PRIMARY KEY (`SequenceID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='统计发号器';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sequence_tako`
--

DROP TABLE IF EXISTS `sequence_tako`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sequence_tako` (
  `SequenceID` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `SequenceName` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'sequence名称',
  `SequenceValue` bigint(20) NOT NULL COMMENT 'sequence值',
  PRIMARY KEY (`SequenceID`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='ID生成';
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-21 23:18:39
