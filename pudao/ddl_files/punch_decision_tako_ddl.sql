-- MySQL dump 10.13  Distrib 5.7.44, for Linux (x86_64)
--
-- Host: **********    Database: punch_decision_tako
-- ------------------------------------------------------
-- Server version	5.7.36-txsql-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Current Database: `punch_decision_tako`
--

CREATE DATABASE /*!32312 IF NOT EXISTS*/ `punch_decision_tako` /*!40100 DEFAULT CHARACTER SET utf8mb4 */;

USE `punch_decision_tako`;

--
-- Table structure for table `interface`
--

DROP TABLE IF EXISTS `interface`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `interface` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `code` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '接口编码',
  `data_source_code` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '所属数据源编码',
  `price` decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '数据源价格',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `unq_name` (`code`,`data_source_code`) USING BTREE,
  KEY `index_dsc` (`data_source_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='接口表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `interface_log_202209`
--

DROP TABLE IF EXISTS `interface_log_202209`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `interface_log_202209` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `data_source_code` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '所属数据源编码',
  `report_type` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '接口编码',
  `product_code` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '产品 编码',
  `customer_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '客户编码',
  `flow_name` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '工作流名称',
  `caller_sn` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '调方序列号',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部请求流水号',
  `apply_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '外部请求流水号',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '数据源接口调用状态',
  `result` int(11) NOT NULL DEFAULT '0' COMMENT '调用结果',
  `request_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据源接口调用时间',
  `cost` int(11) NOT NULL DEFAULT '0' COMMENT '数据源接口调用耗时',
  `input_param` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '输入参数',
  `output_param` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '输出参数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  `price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '单价',
  `billing_method` int(11) NOT NULL DEFAULT '0' COMMENT '计费方式',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `inx_report_type` (`report_type`) USING BTREE,
  KEY `inx_ds_code` (`data_source_code`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_date` (`apply_date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='数据源接口调用记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `interface_log_202210`
--

DROP TABLE IF EXISTS `interface_log_202210`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `interface_log_202210` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `data_source_code` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '所属数据源编码',
  `report_type` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '接口编码',
  `product_code` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '产品 编码',
  `customer_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '客户编码',
  `flow_name` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '工作流名称',
  `caller_sn` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '调方序列号',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部请求流水号',
  `apply_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '外部请求流水号',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '数据源接口调用状态',
  `result` int(11) NOT NULL DEFAULT '0' COMMENT '调用结果',
  `request_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据源接口调用时间',
  `cost` int(11) NOT NULL DEFAULT '0' COMMENT '数据源接口调用耗时',
  `input_param` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '输入参数',
  `output_param` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '输出参数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  `price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '单价',
  `billing_method` int(11) NOT NULL DEFAULT '0' COMMENT '计费方式',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `inx_report_type` (`report_type`) USING BTREE,
  KEY `inx_ds_code` (`data_source_code`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_date` (`apply_date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='数据源接口调用记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `interface_log_202503`
--

DROP TABLE IF EXISTS `interface_log_202503`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `interface_log_202503` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `data_source_code` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '所属数据源编码',
  `report_type` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '接口编码',
  `product_code` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '产品 编码',
  `customer_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '客户编码',
  `flow_name` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '工作流名称',
  `caller_sn` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '调方序列号',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部请求流水号',
  `apply_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '外部请求流水号',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '数据源接口调用状态',
  `result` int(11) NOT NULL DEFAULT '0' COMMENT '调用结果',
  `request_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据源接口调用时间',
  `cost` int(11) NOT NULL DEFAULT '0' COMMENT '数据源接口调用耗时',
  `input_param` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '输入参数',
  `output_param` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '输出参数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  `price` decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '单价',
  `billing_method` int(11) NOT NULL DEFAULT '0' COMMENT '计费方式',
  `in_feature_count` int(11) NOT NULL DEFAULT '0' COMMENT '查询指标数',
  `out_feature_count` int(11) NOT NULL DEFAULT '0' COMMENT '查得指标数',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `inx_report_type` (`report_type`) USING BTREE,
  KEY `inx_ds_code` (`data_source_code`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_date` (`apply_date`) USING BTREE,
  KEY `idx_ct` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='数据源接口调用记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `interface_log_202504`
--

DROP TABLE IF EXISTS `interface_log_202504`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `interface_log_202504` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `data_source_code` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '所属数据源编码',
  `report_type` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '接口编码',
  `product_code` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '产品 编码',
  `customer_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '客户编码',
  `flow_name` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '工作流名称',
  `caller_sn` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '调方序列号',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部请求流水号',
  `apply_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '外部请求流水号',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '数据源接口调用状态',
  `result` int(11) NOT NULL DEFAULT '0' COMMENT '调用结果',
  `request_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据源接口调用时间',
  `cost` int(11) NOT NULL DEFAULT '0' COMMENT '数据源接口调用耗时',
  `input_param` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '输入参数',
  `output_param` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '输出参数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  `price` decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '单价',
  `billing_method` int(11) NOT NULL DEFAULT '0' COMMENT '计费方式',
  `in_feature_count` int(11) NOT NULL DEFAULT '0' COMMENT '查询指标数',
  `out_feature_count` int(11) NOT NULL DEFAULT '0' COMMENT '查得指标数',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `inx_report_type` (`report_type`) USING BTREE,
  KEY `inx_ds_code` (`data_source_code`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_date` (`apply_date`) USING BTREE,
  KEY `idx_ct` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='数据源接口调用记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `interface_log_202505`
--

DROP TABLE IF EXISTS `interface_log_202505`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `interface_log_202505` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `data_source_code` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '所属数据源编码',
  `report_type` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '接口编码',
  `product_code` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '产品 编码',
  `customer_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '客户编码',
  `flow_name` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '工作流名称',
  `caller_sn` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '调方序列号',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部请求流水号',
  `apply_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '外部请求流水号',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '数据源接口调用状态',
  `result` int(11) NOT NULL DEFAULT '0' COMMENT '调用结果',
  `request_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据源接口调用时间',
  `cost` int(11) NOT NULL DEFAULT '0' COMMENT '数据源接口调用耗时',
  `input_param` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '输入参数',
  `output_param` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '输出参数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  `price` decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '单价',
  `billing_method` int(11) NOT NULL DEFAULT '0' COMMENT '计费方式',
  `in_feature_count` int(11) NOT NULL DEFAULT '0' COMMENT '查询指标数',
  `out_feature_count` int(11) NOT NULL DEFAULT '0' COMMENT '查得指标数',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `inx_report_type` (`report_type`) USING BTREE,
  KEY `inx_ds_code` (`data_source_code`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_date` (`apply_date`) USING BTREE,
  KEY `idx_ct` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='数据源接口调用记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `interface_log_202506`
--

DROP TABLE IF EXISTS `interface_log_202506`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `interface_log_202506` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `data_source_code` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '所属数据源编码',
  `report_type` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '接口编码',
  `product_code` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '产品 编码',
  `customer_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '客户编码',
  `flow_name` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '工作流名称',
  `caller_sn` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '调方序列号',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部请求流水号',
  `apply_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '外部请求流水号',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '数据源接口调用状态',
  `result` int(11) NOT NULL DEFAULT '0' COMMENT '调用结果',
  `request_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据源接口调用时间',
  `cost` int(11) NOT NULL DEFAULT '0' COMMENT '数据源接口调用耗时',
  `input_param` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '输入参数',
  `output_param` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '输出参数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  `price` decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '单价',
  `billing_method` int(11) NOT NULL DEFAULT '0' COMMENT '计费方式',
  `in_feature_count` int(11) NOT NULL DEFAULT '0' COMMENT '查询指标数',
  `out_feature_count` int(11) NOT NULL DEFAULT '0' COMMENT '查得指标数',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `inx_report_type` (`report_type`) USING BTREE,
  KEY `inx_ds_code` (`data_source_code`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_date` (`apply_date`) USING BTREE,
  KEY `idx_ct` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='数据源接口调用记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `interface_log_202507`
--

DROP TABLE IF EXISTS `interface_log_202507`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `interface_log_202507` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `data_source_code` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '所属数据源编码',
  `report_type` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '接口编码',
  `product_code` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '产品 编码',
  `customer_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '客户编码',
  `flow_name` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '工作流名称',
  `caller_sn` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '调方序列号',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部请求流水号',
  `apply_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '外部请求流水号',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '数据源接口调用状态',
  `result` int(11) NOT NULL DEFAULT '0' COMMENT '调用结果',
  `request_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据源接口调用时间',
  `cost` int(11) NOT NULL DEFAULT '0' COMMENT '数据源接口调用耗时',
  `input_param` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '输入参数',
  `output_param` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '输出参数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  `price` decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '单价',
  `billing_method` int(11) NOT NULL DEFAULT '0' COMMENT '计费方式',
  `in_feature_count` int(11) NOT NULL DEFAULT '0' COMMENT '查询指标数',
  `out_feature_count` int(11) NOT NULL DEFAULT '0' COMMENT '查得指标数',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `inx_report_type` (`report_type`) USING BTREE,
  KEY `inx_ds_code` (`data_source_code`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_date` (`apply_date`) USING BTREE,
  KEY `idx_ct` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='数据源接口调用记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `interface_log_202508`
--

DROP TABLE IF EXISTS `interface_log_202508`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `interface_log_202508` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `data_source_code` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '所属数据源编码',
  `report_type` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '接口编码',
  `product_code` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '产品 编码',
  `customer_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '客户编码',
  `flow_name` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '工作流名称',
  `caller_sn` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '调方序列号',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部请求流水号',
  `apply_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '外部请求流水号',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '数据源接口调用状态',
  `result` int(11) NOT NULL DEFAULT '0' COMMENT '调用结果',
  `request_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据源接口调用时间',
  `cost` int(11) NOT NULL DEFAULT '0' COMMENT '数据源接口调用耗时',
  `input_param` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '输入参数',
  `output_param` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '输出参数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  `price` decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '单价',
  `billing_method` int(11) NOT NULL DEFAULT '0' COMMENT '计费方式',
  `in_feature_count` int(11) NOT NULL DEFAULT '0' COMMENT '查询指标数',
  `out_feature_count` int(11) NOT NULL DEFAULT '0' COMMENT '查得指标数',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `inx_report_type` (`report_type`) USING BTREE,
  KEY `inx_ds_code` (`data_source_code`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_date` (`apply_date`) USING BTREE,
  KEY `idx_ct` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='数据源接口调用记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `interface_log_202509`
--

DROP TABLE IF EXISTS `interface_log_202509`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `interface_log_202509` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `data_source_code` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '所属数据源编码',
  `report_type` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '接口编码',
  `product_code` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '产品 编码',
  `customer_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '客户编码',
  `flow_name` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '工作流名称',
  `caller_sn` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '调方序列号',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部请求流水号',
  `apply_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '外部请求流水号',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '数据源接口调用状态',
  `result` int(11) NOT NULL DEFAULT '0' COMMENT '调用结果',
  `request_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据源接口调用时间',
  `cost` int(11) NOT NULL DEFAULT '0' COMMENT '数据源接口调用耗时',
  `input_param` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '输入参数',
  `output_param` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '输出参数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  `price` decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '单价',
  `billing_method` int(11) NOT NULL DEFAULT '0' COMMENT '计费方式',
  `in_feature_count` int(11) NOT NULL DEFAULT '0' COMMENT '查询指标数',
  `out_feature_count` int(11) NOT NULL DEFAULT '0' COMMENT '查得指标数',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `inx_report_type` (`report_type`) USING BTREE,
  KEY `inx_ds_code` (`data_source_code`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_date` (`apply_date`) USING BTREE,
  KEY `idx_ct` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='数据源接口调用记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `interface_log_202510`
--

DROP TABLE IF EXISTS `interface_log_202510`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `interface_log_202510` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `data_source_code` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '所属数据源编码',
  `report_type` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '接口编码',
  `product_code` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '产品 编码',
  `customer_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '客户编码',
  `flow_name` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '工作流名称',
  `caller_sn` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '调方序列号',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部请求流水号',
  `apply_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '外部请求流水号',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '数据源接口调用状态',
  `result` int(11) NOT NULL DEFAULT '0' COMMENT '调用结果',
  `request_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据源接口调用时间',
  `cost` int(11) NOT NULL DEFAULT '0' COMMENT '数据源接口调用耗时',
  `input_param` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '输入参数',
  `output_param` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '输出参数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  `price` decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '单价',
  `billing_method` int(11) NOT NULL DEFAULT '0' COMMENT '计费方式',
  `in_feature_count` int(11) NOT NULL DEFAULT '0' COMMENT '查询指标数',
  `out_feature_count` int(11) NOT NULL DEFAULT '0' COMMENT '查得指标数',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `inx_report_type` (`report_type`) USING BTREE,
  KEY `inx_ds_code` (`data_source_code`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_date` (`apply_date`) USING BTREE,
  KEY `idx_ct` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='数据源接口调用记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `interface_log_202511`
--

DROP TABLE IF EXISTS `interface_log_202511`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `interface_log_202511` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `data_source_code` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '所属数据源编码',
  `report_type` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '接口编码',
  `product_code` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '产品 编码',
  `customer_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '客户编码',
  `flow_name` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '工作流名称',
  `caller_sn` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '调方序列号',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部请求流水号',
  `apply_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '外部请求流水号',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '数据源接口调用状态',
  `result` int(11) NOT NULL DEFAULT '0' COMMENT '调用结果',
  `request_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据源接口调用时间',
  `cost` int(11) NOT NULL DEFAULT '0' COMMENT '数据源接口调用耗时',
  `input_param` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '输入参数',
  `output_param` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '输出参数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  `price` decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '单价',
  `billing_method` int(11) NOT NULL DEFAULT '0' COMMENT '计费方式',
  `in_feature_count` int(11) NOT NULL DEFAULT '0' COMMENT '查询指标数',
  `out_feature_count` int(11) NOT NULL DEFAULT '0' COMMENT '查得指标数',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `inx_report_type` (`report_type`) USING BTREE,
  KEY `inx_ds_code` (`data_source_code`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_date` (`apply_date`) USING BTREE,
  KEY `idx_ct` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='数据源接口调用记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `interface_log_202512`
--

DROP TABLE IF EXISTS `interface_log_202512`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `interface_log_202512` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `data_source_code` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '所属数据源编码',
  `report_type` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '接口编码',
  `product_code` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '产品 编码',
  `customer_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '客户编码',
  `flow_name` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '工作流名称',
  `caller_sn` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '调方序列号',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部请求流水号',
  `apply_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '外部请求流水号',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '数据源接口调用状态',
  `result` int(11) NOT NULL DEFAULT '0' COMMENT '调用结果',
  `request_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据源接口调用时间',
  `cost` int(11) NOT NULL DEFAULT '0' COMMENT '数据源接口调用耗时',
  `input_param` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '输入参数',
  `output_param` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '输出参数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  `price` decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '单价',
  `billing_method` int(11) NOT NULL DEFAULT '0' COMMENT '计费方式',
  `in_feature_count` int(11) NOT NULL DEFAULT '0' COMMENT '查询指标数',
  `out_feature_count` int(11) NOT NULL DEFAULT '0' COMMENT '查得指标数',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `inx_report_type` (`report_type`) USING BTREE,
  KEY `inx_ds_code` (`data_source_code`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_date` (`apply_date`) USING BTREE,
  KEY `idx_ct` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='数据源接口调用记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `interface_log_detail_202209`
--

DROP TABLE IF EXISTS `interface_log_detail_202209`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `interface_log_detail_202209` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `log_id` bigint(20) NOT NULL COMMENT 'InterfaceLog Id',
  `data_source_code` varchar(64) NOT NULL DEFAULT '' COMMENT '所属数据源编码',
  `report_type` varchar(64) NOT NULL DEFAULT '' COMMENT '接口编码',
  `feature_key` varchar(32) NOT NULL DEFAULT '' COMMENT '特征编码',
  `feature_val` varchar(32) NOT NULL DEFAULT '' COMMENT '特征值',
  `feature_status` int(11) NOT NULL DEFAULT '0' COMMENT '是否查得',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_log_id` (`log_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='接口调用日志详情';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `interface_log_detail_202210`
--

DROP TABLE IF EXISTS `interface_log_detail_202210`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `interface_log_detail_202210` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `log_id` bigint(20) NOT NULL COMMENT 'InterfaceLog Id',
  `data_source_code` varchar(64) NOT NULL DEFAULT '' COMMENT '所属数据源编码',
  `report_type` varchar(64) NOT NULL DEFAULT '' COMMENT '接口编码',
  `feature_key` varchar(32) NOT NULL DEFAULT '' COMMENT '特征编码',
  `feature_val` varchar(32) NOT NULL DEFAULT '' COMMENT '特征值',
  `feature_status` int(11) NOT NULL DEFAULT '0' COMMENT '是否查得',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_log_id` (`log_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='接口调用日志详情';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `interface_log_detail_202211`
--

DROP TABLE IF EXISTS `interface_log_detail_202211`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `interface_log_detail_202211` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `log_id` bigint(20) NOT NULL COMMENT 'InterfaceLog Id',
  `data_source_code` varchar(64) NOT NULL DEFAULT '' COMMENT '所属数据源编码',
  `report_type` varchar(64) NOT NULL DEFAULT '' COMMENT '接口编码',
  `feature_key` varchar(32) NOT NULL DEFAULT '' COMMENT '特征编码',
  `feature_val` varchar(32) NOT NULL DEFAULT '' COMMENT '特征值',
  `feature_status` int(11) NOT NULL DEFAULT '0' COMMENT '是否查得',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_log_id` (`log_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='接口调用日志详情';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `interface_log_detail_202212`
--

DROP TABLE IF EXISTS `interface_log_detail_202212`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `interface_log_detail_202212` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `log_id` bigint(20) NOT NULL COMMENT 'InterfaceLog Id',
  `data_source_code` varchar(64) NOT NULL DEFAULT '' COMMENT '所属数据源编码',
  `report_type` varchar(64) NOT NULL DEFAULT '' COMMENT '接口编码',
  `feature_key` varchar(32) NOT NULL DEFAULT '' COMMENT '特征编码',
  `feature_val` varchar(32) NOT NULL DEFAULT '' COMMENT '特征值',
  `feature_status` int(11) NOT NULL DEFAULT '0' COMMENT '是否查得',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_log_id` (`log_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='接口调用日志详情';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `interface_log_detail_202301`
--

DROP TABLE IF EXISTS `interface_log_detail_202301`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `interface_log_detail_202301` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `log_id` bigint(20) NOT NULL COMMENT 'InterfaceLog Id',
  `data_source_code` varchar(64) NOT NULL DEFAULT '' COMMENT '所属数据源编码',
  `report_type` varchar(64) NOT NULL DEFAULT '' COMMENT '接口编码',
  `feature_key` varchar(32) NOT NULL DEFAULT '' COMMENT '特征编码',
  `feature_val` varchar(32) NOT NULL DEFAULT '' COMMENT '特征值',
  `feature_status` int(11) NOT NULL DEFAULT '0' COMMENT '是否查得',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_log_id` (`log_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='接口调用日志详情';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `interface_log_detail_202503`
--

DROP TABLE IF EXISTS `interface_log_detail_202503`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `interface_log_detail_202503` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `log_id` bigint(20) NOT NULL COMMENT 'InterfaceLog Id',
  `data_source_code` varchar(64) NOT NULL DEFAULT '' COMMENT '所属数据源编码',
  `report_type` varchar(64) NOT NULL DEFAULT '' COMMENT '接口编码',
  `feature_key` varchar(32) NOT NULL DEFAULT '' COMMENT '特征编码',
  `feature_val` varchar(32) NOT NULL DEFAULT '' COMMENT '特征值',
  `feature_status` int(11) NOT NULL DEFAULT '0' COMMENT '是否查得',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_log_id` (`log_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='接口调用日志详情';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `interface_log_detail_202504`
--

DROP TABLE IF EXISTS `interface_log_detail_202504`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `interface_log_detail_202504` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `log_id` bigint(20) NOT NULL COMMENT 'InterfaceLog Id',
  `data_source_code` varchar(64) NOT NULL DEFAULT '' COMMENT '所属数据源编码',
  `report_type` varchar(64) NOT NULL DEFAULT '' COMMENT '接口编码',
  `feature_key` varchar(32) NOT NULL DEFAULT '' COMMENT '特征编码',
  `feature_val` varchar(32) NOT NULL DEFAULT '' COMMENT '特征值',
  `feature_status` int(11) NOT NULL DEFAULT '0' COMMENT '是否查得',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_log_id` (`log_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='接口调用日志详情';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `interface_log_detail_202505`
--

DROP TABLE IF EXISTS `interface_log_detail_202505`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `interface_log_detail_202505` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `log_id` bigint(20) NOT NULL COMMENT 'InterfaceLog Id',
  `data_source_code` varchar(64) NOT NULL DEFAULT '' COMMENT '所属数据源编码',
  `report_type` varchar(64) NOT NULL DEFAULT '' COMMENT '接口编码',
  `feature_key` varchar(32) NOT NULL DEFAULT '' COMMENT '特征编码',
  `feature_val` varchar(32) NOT NULL DEFAULT '' COMMENT '特征值',
  `feature_status` int(11) NOT NULL DEFAULT '0' COMMENT '是否查得',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_log_id` (`log_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='接口调用日志详情';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `interface_log_detail_202506`
--

DROP TABLE IF EXISTS `interface_log_detail_202506`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `interface_log_detail_202506` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `log_id` bigint(20) NOT NULL COMMENT 'InterfaceLog Id',
  `data_source_code` varchar(64) NOT NULL DEFAULT '' COMMENT '所属数据源编码',
  `report_type` varchar(64) NOT NULL DEFAULT '' COMMENT '接口编码',
  `feature_key` varchar(32) NOT NULL DEFAULT '' COMMENT '特征编码',
  `feature_val` varchar(32) NOT NULL DEFAULT '' COMMENT '特征值',
  `feature_status` int(11) NOT NULL DEFAULT '0' COMMENT '是否查得',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_log_id` (`log_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='接口调用日志详情';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `interface_log_detail_202507`
--

DROP TABLE IF EXISTS `interface_log_detail_202507`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `interface_log_detail_202507` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `log_id` bigint(20) NOT NULL COMMENT 'InterfaceLog Id',
  `data_source_code` varchar(64) NOT NULL DEFAULT '' COMMENT '所属数据源编码',
  `report_type` varchar(64) NOT NULL DEFAULT '' COMMENT '接口编码',
  `feature_key` varchar(32) NOT NULL DEFAULT '' COMMENT '特征编码',
  `feature_val` varchar(32) NOT NULL DEFAULT '' COMMENT '特征值',
  `feature_status` int(11) NOT NULL DEFAULT '0' COMMENT '是否查得',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_log_id` (`log_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='接口调用日志详情';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `interface_log_detail_202508`
--

DROP TABLE IF EXISTS `interface_log_detail_202508`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `interface_log_detail_202508` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `log_id` bigint(20) NOT NULL COMMENT 'InterfaceLog Id',
  `data_source_code` varchar(64) NOT NULL DEFAULT '' COMMENT '所属数据源编码',
  `report_type` varchar(64) NOT NULL DEFAULT '' COMMENT '接口编码',
  `feature_key` varchar(32) NOT NULL DEFAULT '' COMMENT '特征编码',
  `feature_val` varchar(32) NOT NULL DEFAULT '' COMMENT '特征值',
  `feature_status` int(11) NOT NULL DEFAULT '0' COMMENT '是否查得',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_log_id` (`log_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='接口调用日志详情';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `interface_log_detail_202509`
--

DROP TABLE IF EXISTS `interface_log_detail_202509`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `interface_log_detail_202509` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `log_id` bigint(20) NOT NULL COMMENT 'InterfaceLog Id',
  `data_source_code` varchar(64) NOT NULL DEFAULT '' COMMENT '所属数据源编码',
  `report_type` varchar(64) NOT NULL DEFAULT '' COMMENT '接口编码',
  `feature_key` varchar(32) NOT NULL DEFAULT '' COMMENT '特征编码',
  `feature_val` varchar(32) NOT NULL DEFAULT '' COMMENT '特征值',
  `feature_status` int(11) NOT NULL DEFAULT '0' COMMENT '是否查得',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_log_id` (`log_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='接口调用日志详情';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `interface_log_detail_202510`
--

DROP TABLE IF EXISTS `interface_log_detail_202510`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `interface_log_detail_202510` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `log_id` bigint(20) NOT NULL COMMENT 'InterfaceLog Id',
  `data_source_code` varchar(64) NOT NULL DEFAULT '' COMMENT '所属数据源编码',
  `report_type` varchar(64) NOT NULL DEFAULT '' COMMENT '接口编码',
  `feature_key` varchar(32) NOT NULL DEFAULT '' COMMENT '特征编码',
  `feature_val` varchar(32) NOT NULL DEFAULT '' COMMENT '特征值',
  `feature_status` int(11) NOT NULL DEFAULT '0' COMMENT '是否查得',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_log_id` (`log_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='接口调用日志详情';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `interface_log_detail_202511`
--

DROP TABLE IF EXISTS `interface_log_detail_202511`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `interface_log_detail_202511` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `log_id` bigint(20) NOT NULL COMMENT 'InterfaceLog Id',
  `data_source_code` varchar(64) NOT NULL DEFAULT '' COMMENT '所属数据源编码',
  `report_type` varchar(64) NOT NULL DEFAULT '' COMMENT '接口编码',
  `feature_key` varchar(32) NOT NULL DEFAULT '' COMMENT '特征编码',
  `feature_val` varchar(32) NOT NULL DEFAULT '' COMMENT '特征值',
  `feature_status` int(11) NOT NULL DEFAULT '0' COMMENT '是否查得',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_log_id` (`log_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='接口调用日志详情';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `interface_log_detail_202512`
--

DROP TABLE IF EXISTS `interface_log_detail_202512`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `interface_log_detail_202512` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `log_id` bigint(20) NOT NULL COMMENT 'InterfaceLog Id',
  `data_source_code` varchar(64) NOT NULL DEFAULT '' COMMENT '所属数据源编码',
  `report_type` varchar(64) NOT NULL DEFAULT '' COMMENT '接口编码',
  `feature_key` varchar(32) NOT NULL DEFAULT '' COMMENT '特征编码',
  `feature_val` varchar(32) NOT NULL DEFAULT '' COMMENT '特征值',
  `feature_status` int(11) NOT NULL DEFAULT '0' COMMENT '是否查得',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_log_id` (`log_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='接口调用日志详情';
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-21 23:18:40
