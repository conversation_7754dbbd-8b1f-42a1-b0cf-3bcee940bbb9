-- MySQL dump 10.13  Distrib 5.7.44, for Linux (x86_64)
--
-- Host: **********    Database: punch_decision_argus
-- ------------------------------------------------------
-- Server version	5.7.36-txsql-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Current Database: `punch_decision_argus`
--

CREATE DATABASE /*!32312 IF NOT EXISTS*/ `punch_decision_argus` /*!40100 DEFAULT CHARACTER SET utf8mb4 */;

USE `punch_decision_argus`;

--
-- Table structure for table `monitor`
--

DROP TABLE IF EXISTS `monitor`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `monitor` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `monitor_code` varchar(100) NOT NULL COMMENT '监控编码',
  `monitor_name` varchar(100) NOT NULL COMMENT '监控名称',
  `monitor_product_code` varchar(500) NOT NULL COMMENT '监控产品编码list，逗号隔开',
  `monitor_begin_time` datetime DEFAULT NULL COMMENT '监控开始时间',
  `monitor_end_time` datetime DEFAULT NULL COMMENT '监控结束时间',
  `creator` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建者',
  `updater` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_monitor_code` (`monitor_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='监控配置';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `monitor_chart`
--

DROP TABLE IF EXISTS `monitor_chart`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `monitor_chart` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `monitor_id` bigint(20) NOT NULL COMMENT '监控配置ID',
  `monitor_chart_code` varchar(100) NOT NULL COMMENT '监控图表配置编码',
  `monitor_chart_name` varchar(100) NOT NULL COMMENT '监控图表配置名称',
  `monitor_chart_type` tinyint(4) NOT NULL COMMENT '监控图表配置类型',
  `creator` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建者',
  `updater` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_monitor_chart_code` (`monitor_chart_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='图表配置';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `monitor_chart_item`
--

DROP TABLE IF EXISTS `monitor_chart_item`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `monitor_chart_item` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `monitor_id` bigint(20) NOT NULL COMMENT '监控配置ID',
  `monitor_chart_id` bigint(20) NOT NULL COMMENT '监控图表配置ID',
  `monitor_item_id` bigint(20) NOT NULL COMMENT '监控项配置ID',
  `creator` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建者',
  `updater` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='图表配置-监控项';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `monitor_filter`
--

DROP TABLE IF EXISTS `monitor_filter`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `monitor_filter` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `monitor_id` bigint(20) NOT NULL COMMENT '监控配置ID',
  `monitor_item_id` bigint(20) NOT NULL COMMENT '监控项配置ID',
  `monitor_filter_group_type` tinyint(4) NOT NULL COMMENT '监控过滤组类型',
  `creator` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建者',
  `updater` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='监控项 filter';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `monitor_filter_group`
--

DROP TABLE IF EXISTS `monitor_filter_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `monitor_filter_group` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `monitor_id` bigint(20) NOT NULL COMMENT '监控配置ID',
  `monitor_item_id` bigint(20) NOT NULL COMMENT '监控项配置ID',
  `monitor_filter_id` bigint(20) NOT NULL COMMENT '监控filter ID',
  `monitor_filter_type` tinyint(4) NOT NULL COMMENT '监控字段类型',
  `monitor_filter_filed` varchar(100) NOT NULL COMMENT '监控字段名称',
  `monitor_filter_filed_type` tinyint(4) NOT NULL COMMENT '监控字段类型',
  `monitor_filter_filed_kv` tinyint(4) DEFAULT NULL COMMENT '监控filter字段为map时标记key、value',
  `monitor_filter_predicate` tinyint(4) NOT NULL COMMENT '监控filter谓词',
  `monitor_filter_value` varchar(100) NOT NULL DEFAULT '' COMMENT '监控filter 值',
  `creator` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建者',
  `updater` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='监控项 filter group';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `monitor_group`
--

DROP TABLE IF EXISTS `monitor_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `monitor_group` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `monitor_id` bigint(20) NOT NULL COMMENT '监控配置ID',
  `monitor_item_id` bigint(20) NOT NULL COMMENT '监控项配置ID',
  `monitor_group_type` tinyint(4) NOT NULL COMMENT '监控分组类型',
  `monitor_group_filed` varchar(100) NOT NULL COMMENT '监控分组字段名称',
  `monitor_group_filed_type` tinyint(4) NOT NULL COMMENT '监控分组字段类型',
  `monitor_group_filed_kv` tinyint(4) DEFAULT NULL COMMENT '监控分组字段k-v类型',
  `monitor_group_standard_type` tinyint(4) DEFAULT NULL COMMENT '监控分组基准值类型',
  `creator` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建者',
  `updater` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='监控项 groupBy';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `monitor_group_detail`
--

DROP TABLE IF EXISTS `monitor_group_detail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `monitor_group_detail` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `monitor_id` bigint(20) NOT NULL COMMENT '监控配置ID',
  `monitor_item_id` bigint(20) NOT NULL COMMENT '监控项配置ID',
  `monitor_group_id` bigint(20) NOT NULL COMMENT '监控分组ID',
  `monitor_group_name` varchar(100) NOT NULL COMMENT '监控分组名称',
  `monitor_group_content` varchar(500) NOT NULL COMMENT '监控分组规则',
  `monitor_group_standard` decimal(20,4) NOT NULL DEFAULT '0.0000' COMMENT '监控分组基准值',
  `monitor_group_standard_type` tinyint(4) DEFAULT NULL COMMENT '监控分组基准值类型',
  `creator` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建者',
  `updater` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='监控项groupBy详情';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `monitor_item`
--

DROP TABLE IF EXISTS `monitor_item`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `monitor_item` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `monitor_id` bigint(20) NOT NULL COMMENT '监控配置ID',
  `monitor_item_code` varchar(100) NOT NULL COMMENT '监控项编码',
  `monitor_item_name` varchar(100) NOT NULL COMMENT '监控项名称',
  `creator` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建者',
  `updater` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_monitor_item_code` (`monitor_item_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='监控项配置';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `monitor_reduce`
--

DROP TABLE IF EXISTS `monitor_reduce`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `monitor_reduce` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `monitor_id` bigint(20) NOT NULL COMMENT '监控配置ID',
  `monitor_item_id` bigint(20) NOT NULL COMMENT '监控项配置ID',
  `monitor_reduce_type` tinyint(4) NOT NULL COMMENT '监控reduce类型',
  `monitor_reduce_filed` varchar(100) NOT NULL COMMENT '监控字段',
  `monitor_reduce_filed_type` tinyint(4) NOT NULL COMMENT '监控reduce字段类型',
  `monitor_reduce_filed_kv` tinyint(4) DEFAULT NULL COMMENT '监控字段若为map，此字段标记key, value',
  `creator` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建者',
  `updater` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='监控项 reduce';
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-21 23:18:40
