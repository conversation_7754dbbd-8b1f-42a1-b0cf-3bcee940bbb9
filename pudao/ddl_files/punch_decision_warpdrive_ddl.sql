-- MySQL dump 10.13  Distrib 5.7.44, for Linux (x86_64)
--
-- Host: **********    Database: punch_decision_warpdrive
-- ------------------------------------------------------
-- Server version	5.7.36-txsql-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Current Database: `punch_decision_warpdrive`
--

CREATE DATABASE /*!32312 IF NOT EXISTS*/ `punch_decision_warpdrive` /*!40100 DEFAULT CHARACTER SET utf8mb4 */;

USE `punch_decision_warpdrive`;

--
-- Table structure for table `exception_log`
--

DROP TABLE IF EXISTS `exception_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `exception_log` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `app_name` varchar(64) NOT NULL DEFAULT '' COMMENT '应用名',
  `exception` varchar(128) NOT NULL DEFAULT '' COMMENT '异常名',
  `exp_msg` varchar(128) NOT NULL DEFAULT '' COMMENT '异常msg',
  `stack_trace` varchar(1024) NOT NULL DEFAULT '' COMMENT '异常栈',
  `request_uri` varchar(255) NOT NULL DEFAULT '' COMMENT '请求链接',
  `remote_ip` varchar(40) NOT NULL DEFAULT '' COMMENT '请求方ip',
  `request_param` varchar(255) NOT NULL DEFAULT '' COMMENT '请求参数',
  `host` varchar(50) NOT NULL DEFAULT '' COMMENT '应用host',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='异常日志';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `indicator`
--

DROP TABLE IF EXISTS `indicator`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `indicator` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `model_id` bigint(20) NOT NULL COMMENT '模型ID',
  `model_name` varchar(50) NOT NULL COMMENT '模型名称',
  `model_file_id` bigint(20) NOT NULL COMMENT '模型文件ID',
  `model_file_name` varchar(50) NOT NULL COMMENT '模型文件名称',
  `indicator_name` varchar(100) NOT NULL COMMENT '指标名称',
  `indicator_content` varchar(500) DEFAULT NULL COMMENT '指标内容',
  `creator` varchar(50) NOT NULL COMMENT '创建者',
  `updater` varchar(50) NOT NULL COMMENT '更新者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='指标表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `model`
--

DROP TABLE IF EXISTS `model`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `model` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `model_name` varchar(50) NOT NULL COMMENT '模型名称',
  `model_type` tinyint(4) DEFAULT NULL COMMENT '模型状态, 枚举：1-打分，2-策略',
  `model_status` tinyint(4) NOT NULL COMMENT '模型类型，枚举：1-已创建，2-已测试，3-已发布',
  `score_card_type` tinyint(4) NOT NULL COMMENT '打分卡类型, 枚举：1-A，2-B，3-C',
  `product_type` tinyint(4) NOT NULL COMMENT '产品类型, 枚举：1-自营业务，2-数据产品',
  `algorithm_type` tinyint(4) NOT NULL COMMENT '算法类型, 枚举：1-Logistic，2-Xgboost',
  `min_score` decimal(6,2) DEFAULT NULL COMMENT '模型最低分',
  `max_score` decimal(6,2) DEFAULT NULL COMMENT '模型最高分',
  `min_score_range` decimal(6,2) DEFAULT NULL COMMENT '处理结果最低分',
  `max_score_range` decimal(6,2) DEFAULT NULL COMMENT '处理结果最高分',
  `result_deal_type` tinyint(4) DEFAULT NULL COMMENT '结果处理方式，枚举：1-不处理，2-值转换，3-黑名单&值转换',
  `publish_name` varchar(20) DEFAULT NULL COMMENT '发布者',
  `publish_time` datetime DEFAULT NULL COMMENT '发布时间',
  `author` varchar(50) NOT NULL COMMENT '开发者',
  `remark` varchar(200) NOT NULL DEFAULT '' COMMENT '备注',
  `creator` varchar(50) NOT NULL COMMENT '创建者',
  `updater` varchar(50) NOT NULL COMMENT '更新者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_model_name` (`model_name`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='模型表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `model_file`
--

DROP TABLE IF EXISTS `model_file`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `model_file` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `model_id` bigint(20) NOT NULL COMMENT '模型ID',
  `model_file_name` varchar(50) NOT NULL COMMENT '模型文件名称',
  `model_file_content` mediumblob COMMENT '模型文件内容',
  `creator` varchar(50) NOT NULL COMMENT '创建者',
  `updater` varchar(50) NOT NULL COMMENT '更新者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='模型文件表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `model_operation`
--

DROP TABLE IF EXISTS `model_operation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `model_operation` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `model_id` bigint(20) NOT NULL COMMENT '模型ID',
  `model_operate_type` tinyint(4) NOT NULL COMMENT '模型操作日志',
  `creator` varchar(50) NOT NULL COMMENT '创建者',
  `updater` varchar(50) NOT NULL COMMENT '更新者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='模型操作日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `operation_log`
--

DROP TABLE IF EXISTS `operation_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `operation_log` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `app_name` varchar(50) NOT NULL DEFAULT '' COMMENT '应用名称',
  `username` varchar(50) NOT NULL DEFAULT '' COMMENT '操作用户',
  `operate` varchar(100) NOT NULL DEFAULT '' COMMENT '操作行为',
  `host` varchar(255) NOT NULL DEFAULT '' COMMENT '请求host',
  `request_api` varchar(255) NOT NULL DEFAULT '' COMMENT '请求接口',
  `request_params` varchar(1000) NOT NULL DEFAULT '' COMMENT '请求参数',
  `remote_ip` varchar(50) NOT NULL DEFAULT '' COMMENT '操作IP',
  `period` bigint(20) NOT NULL DEFAULT '0' COMMENT '请求时长',
  `log_level` tinyint(4) NOT NULL COMMENT '日志等级',
  `log_type` tinyint(4) NOT NULL COMMENT '日志类型',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `test_file`
--

DROP TABLE IF EXISTS `test_file`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `test_file` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `model_id` bigint(20) NOT NULL COMMENT '模型ID',
  `test_file_name` varchar(50) NOT NULL COMMENT '测试文件名称',
  `test_file_content` mediumblob COMMENT '测试文件内容',
  `test_result` mediumblob COMMENT '测试结果',
  `creator` varchar(50) NOT NULL COMMENT '创建者',
  `updater` varchar(50) NOT NULL COMMENT '更新者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_model_id` (`model_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='测试文件表';
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-21 23:18:40
