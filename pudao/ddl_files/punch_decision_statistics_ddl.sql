-- MySQL dump 10.13  Distrib 5.7.44, for Linux (x86_64)
--
-- Host: **********    Database: punch_decision_statistics
-- ------------------------------------------------------
-- Server version	5.7.36-txsql-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Current Database: `punch_decision_statistics`
--

CREATE DATABASE /*!32312 IF NOT EXISTS*/ `punch_decision_statistics` /*!40100 DEFAULT CHARACTER SET utf8mb4 */;

USE `punch_decision_statistics`;

--
-- Table structure for table `customer_product_info`
--

DROP TABLE IF EXISTS `customer_product_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `customer_product_info` (
  `id` bigint(20) unsigned NOT NULL COMMENT '主键ID',
  `customer` varchar(45) NOT NULL DEFAULT '' COMMENT '客户编码',
  `c_name` varchar(45) NOT NULL DEFAULT '' COMMENT '客户名称',
  `product` varchar(45) NOT NULL DEFAULT '' COMMENT '产品编码',
  `p_name` varchar(45) NOT NULL DEFAULT '' COMMENT '产品名称',
  `call_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '调用总次数',
  `succ_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '成功调用量',
  `fail_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '调用失败次数',
  `succ_billing_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '成功且计费量',
  `succ_unbilling_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '成功不计费量',
  `month_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '当月调用量',
  `cost_avg` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '平均耗时',
  `max_cost` bigint(20) NOT NULL DEFAULT '0' COMMENT '最大耗时',
  `fail_rate` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '调用失败率',
  `success_rate` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '调用成功率',
  `billing_rate` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '计费率',
  `lately7_call_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '近7日平均调用量',
  `lately_fail_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '近7日失败量',
  `lately30_avg_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '近30日平均调用量',
  `lately15_billing_avg` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '近15日平均计费率',
  `consumption` decimal(20,6) NOT NULL DEFAULT '0.000000' COMMENT '当日消费金额',
  `month_consumption` decimal(20,6) NOT NULL DEFAULT '0.000000' COMMENT '当月消费金额',
  `year_consumption` decimal(20,6) NOT NULL DEFAULT '0.000000' COMMENT '当年消费金额',
  `call_date` date NOT NULL DEFAULT '1900-01-01' COMMENT '调用日期',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `feature_call_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '特征调用总量',
  `feature_success_billing_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '特征成功且计费量',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `inx_call_date` (`call_date`) USING BTREE,
  KEY `inx_ct` (`created_at`) USING BTREE,
  KEY `idx_pcc` (`product`,`customer`,`call_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='客户产品调用情况统计表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `datasource_customer_product`
--

DROP TABLE IF EXISTS `datasource_customer_product`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `datasource_customer_product` (
  `id` bigint(20) unsigned NOT NULL COMMENT '主键ID',
  `customer` varchar(32) NOT NULL DEFAULT '' COMMENT '客户',
  `product` varchar(32) NOT NULL DEFAULT '' COMMENT '产品',
  `data_source_code` varchar(32) NOT NULL DEFAULT '' COMMENT '所属数据源编码',
  `report_type` varchar(50) NOT NULL DEFAULT '' COMMENT '接口编码',
  `call_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '调用总次数',
  `succ_billing_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '成功且计费量',
  `succ_unbilling_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '成功不计费量',
  `fail_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '调用失败次数',
  `success_rate` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '调用成功率',
  `billing_rate` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '计费率',
  `cost_avg` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '平均耗时',
  `max_cost` int(11) NOT NULL DEFAULT '0' COMMENT '最大耗时',
  `call_date` date NOT NULL DEFAULT '1900-01-01' COMMENT '调用日期',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `feature_call_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '特征调用总量',
  `feature_success_billing_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '特征成功且计费量',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `inx_call_date` (`call_date`) USING BTREE,
  KEY `inx_ct` (`created_at`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='数据源客户产品调用情况统计表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `statistics_apply_summary`
--

DROP TABLE IF EXISTS `statistics_apply_summary`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `statistics_apply_summary` (
  `id` bigint(20) unsigned NOT NULL COMMENT '主键ID',
  `call_succ_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '成功调用次数',
  `call_fail_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '失败调用次数',
  `total_cost` bigint(20) NOT NULL DEFAULT '0' COMMENT '总耗时',
  `max_cost` int(11) NOT NULL DEFAULT '0' COMMENT '最大耗时',
  `min_cost` int(11) NOT NULL DEFAULT '0' COMMENT '最小耗时',
  `charge_call_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '计费调用次数',
  `amount` decimal(20,6) NOT NULL DEFAULT '0.000000' COMMENT '计费金额',
  `call_date` date NOT NULL DEFAULT '1900-01-01' COMMENT '调用日期',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `inx_call_date` (`call_date`) USING BTREE,
  KEY `inx_ct` (`created_at`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='产品接口调用情况汇总表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `statistics_customer_info`
--

DROP TABLE IF EXISTS `statistics_customer_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `statistics_customer_info` (
  `id` bigint(20) unsigned NOT NULL COMMENT '主键ID',
  `customer` varchar(32) NOT NULL DEFAULT '' COMMENT '客户',
  `call_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '调用总次数',
  `succ_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '调用成功次数',
  `fail_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '调用失败次数',
  `success_rate` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '调用成功率',
  `fail_rate` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '调用失败率',
  `billing_rate` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '计费率',
  `cost_avg` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '平均耗时',
  `max_cost` int(11) NOT NULL DEFAULT '0' COMMENT '最大耗时',
  `call_date` date NOT NULL DEFAULT '1900-01-01' COMMENT '调用日期',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `inx_call_date` (`call_date`) USING BTREE,
  KEY `inx_ct` (`created_at`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='客户产品调用情况统计表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `statistics_datasource_summary`
--

DROP TABLE IF EXISTS `statistics_datasource_summary`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `statistics_datasource_summary` (
  `id` bigint(20) unsigned NOT NULL COMMENT '主键ID',
  `call_succ_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '成功调用次数',
  `call_fail_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '失败调用次数',
  `total_cost` bigint(20) NOT NULL DEFAULT '0' COMMENT '总耗时',
  `max_cost` int(11) NOT NULL DEFAULT '0' COMMENT '最大耗时',
  `min_cost` int(11) NOT NULL DEFAULT '0' COMMENT '最小耗时',
  `charge_call_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '计费调用次数',
  `amount` decimal(20,6) NOT NULL DEFAULT '0.000000' COMMENT '计费金额',
  `call_date` date NOT NULL DEFAULT '1900-01-01' COMMENT '调用日期',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `inx_call_date` (`call_date`) USING BTREE,
  KEY `inx_ct` (`created_at`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='数据源接口调用情况统计表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `statistics_interface_info`
--

DROP TABLE IF EXISTS `statistics_interface_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `statistics_interface_info` (
  `id` bigint(20) unsigned NOT NULL COMMENT '主键ID',
  `data_source_code` varchar(32) NOT NULL DEFAULT '' COMMENT '所属数据源编码',
  `report_type` varchar(50) NOT NULL DEFAULT '' COMMENT '接口编码',
  `call_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '调用总次数',
  `succ_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '调用成功次数',
  `fail_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '调用失败次数',
  `lately7_call_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '近7日平均调用量',
  `month_call_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '当月调用总量',
  `lately7_fail_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '近7日平均失败量',
  `success_rate` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '调用成功率',
  `fail_rate` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '调用失败率',
  `billing_rate` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '计费率',
  `lately15_billing_rate` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '近15日计费率',
  `cost_avg` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '平均耗时',
  `max_cost` int(11) NOT NULL DEFAULT '0' COMMENT '最大耗时',
  `call_date` date NOT NULL DEFAULT '1900-01-01' COMMENT '调用日期',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `inx_cd` (`call_date`) USING BTREE,
  KEY `inx_ct` (`created_at`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='数据源接口调用情况统计表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `statistics_product_info`
--

DROP TABLE IF EXISTS `statistics_product_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `statistics_product_info` (
  `id` bigint(20) unsigned NOT NULL COMMENT '主键ID',
  `product` varchar(45) NOT NULL DEFAULT '' COMMENT '产品编码',
  `name` varchar(45) NOT NULL DEFAULT '' COMMENT '产品名称',
  `call_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '调用总次数',
  `call_feature_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '当天特征调用量',
  `lately7_call_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '近7日平均调用量',
  `lately7_feature_call_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '近7日特征平均调用量',
  `month_call_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '当月调用总量',
  `month_feature_call_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '当月特征调用量',
  `succ_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '调用成功次数',
  `fail_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '调用失败次数',
  `consumption` decimal(20,6) NOT NULL DEFAULT '0.000000' COMMENT '当日消费金额',
  `month_consumption` decimal(20,6) NOT NULL DEFAULT '0.000000' COMMENT '当月消费金额',
  `success_rate` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '调用成功率',
  `fail_rate` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '调用失败率',
  `billing_rate` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '计费率',
  `cost_avg` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '平均耗时',
  `max_cost` int(11) NOT NULL DEFAULT '0' COMMENT '最大耗时',
  `call_date` date NOT NULL DEFAULT '1900-01-01' COMMENT '调用日期',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `inx_call_date` (`call_date`) USING BTREE,
  KEY `inx_ct` (`created_at`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='产品调用情况统计表';
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-21 23:18:40
