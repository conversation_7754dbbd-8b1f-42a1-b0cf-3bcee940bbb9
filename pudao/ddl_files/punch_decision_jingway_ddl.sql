-- MySQL dump 10.13  Distrib 5.7.44, for Linux (x86_64)
--
-- Host: **********    Database: punch_decision_jingway
-- ------------------------------------------------------
-- Server version	5.7.36-txsql-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Current Database: `punch_decision_jingway`
--

CREATE DATABASE /*!32312 IF NOT EXISTS*/ `punch_decision_jingway` /*!40100 DEFAULT CHARACTER SET utf8mb4 */;

USE `punch_decision_jingway`;

--
-- Table structure for table `action_logs_202209`
--

DROP TABLE IF EXISTS `action_logs_202209`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `action_logs_202209` (
  `action_log_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `node_id` bigint(20) NOT NULL COMMENT '节点id',
  `tag` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '业务标记',
  `service_type` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '服务类型',
  `function` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '功能类型',
  `input_param` mediumtext COLLATE utf8mb4_bin COMMENT '调用参数',
  `output` mediumtext COLLATE utf8mb4_bin COMMENT '调用结果',
  `cost_time` int(11) NOT NULL COMMENT '动作耗时',
  `err_msg` varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '错误描述',
  `tako_sn` varchar(50) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '外部存储tako编码',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  `feature_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '指标总量',
  `fee_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '计费量',
  PRIMARY KEY (`action_log_id`) USING BTREE,
  KEY `idx_product` (`product`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='动作记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `action_logs_202210`
--

DROP TABLE IF EXISTS `action_logs_202210`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `action_logs_202210` (
  `action_log_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `node_id` bigint(20) NOT NULL COMMENT '节点id',
  `tag` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '业务标记',
  `service_type` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '服务类型',
  `function` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '功能类型',
  `input_param` mediumtext COLLATE utf8mb4_bin COMMENT '调用参数',
  `output` mediumtext COLLATE utf8mb4_bin COMMENT '调用结果',
  `cost_time` int(11) NOT NULL COMMENT '动作耗时',
  `err_msg` varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '错误描述',
  `tako_sn` varchar(50) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '外部存储tako编码',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  `feature_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '指标总量',
  `fee_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '计费量',
  PRIMARY KEY (`action_log_id`) USING BTREE,
  KEY `idx_product` (`product`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='动作记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `action_logs_202503`
--

DROP TABLE IF EXISTS `action_logs_202503`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `action_logs_202503` (
  `action_log_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `node_id` bigint(20) NOT NULL COMMENT '节点id',
  `tag` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '业务标记',
  `service_type` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '服务类型',
  `function` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '功能类型',
  `input_param` mediumtext COLLATE utf8mb4_bin COMMENT '调用参数',
  `output` mediumtext COLLATE utf8mb4_bin COMMENT '调用结果',
  `cost_time` int(11) NOT NULL COMMENT '动作耗时',
  `err_msg` varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '错误描述',
  `tako_sn` varchar(50) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '外部存储tako编码',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  `feature_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '指标总量',
  `fee_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '计费量',
  PRIMARY KEY (`action_log_id`) USING BTREE,
  KEY `idx_product` (`product`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='动作记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `action_logs_202504`
--

DROP TABLE IF EXISTS `action_logs_202504`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `action_logs_202504` (
  `action_log_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `node_id` bigint(20) NOT NULL COMMENT '节点id',
  `tag` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '业务标记',
  `service_type` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '服务类型',
  `function` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '功能类型',
  `input_param` mediumtext COLLATE utf8mb4_bin COMMENT '调用参数',
  `output` mediumtext COLLATE utf8mb4_bin COMMENT '调用结果',
  `cost_time` int(11) NOT NULL COMMENT '动作耗时',
  `err_msg` varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '错误描述',
  `tako_sn` varchar(50) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '外部存储tako编码',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  `feature_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '指标总量',
  `fee_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '计费量',
  PRIMARY KEY (`action_log_id`) USING BTREE,
  KEY `idx_product` (`product`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='动作记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `action_logs_202505`
--

DROP TABLE IF EXISTS `action_logs_202505`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `action_logs_202505` (
  `action_log_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `node_id` bigint(20) NOT NULL COMMENT '节点id',
  `tag` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '业务标记',
  `service_type` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '服务类型',
  `function` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '功能类型',
  `input_param` mediumtext COLLATE utf8mb4_bin COMMENT '调用参数',
  `output` mediumtext COLLATE utf8mb4_bin COMMENT '调用结果',
  `cost_time` int(11) NOT NULL COMMENT '动作耗时',
  `err_msg` varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '错误描述',
  `tako_sn` varchar(50) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '外部存储tako编码',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  `feature_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '指标总量',
  `fee_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '计费量',
  PRIMARY KEY (`action_log_id`) USING BTREE,
  KEY `idx_product` (`product`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='动作记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `action_logs_202506`
--

DROP TABLE IF EXISTS `action_logs_202506`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `action_logs_202506` (
  `action_log_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `node_id` bigint(20) NOT NULL COMMENT '节点id',
  `tag` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '业务标记',
  `service_type` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '服务类型',
  `function` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '功能类型',
  `input_param` mediumtext COLLATE utf8mb4_bin COMMENT '调用参数',
  `output` mediumtext COLLATE utf8mb4_bin COMMENT '调用结果',
  `cost_time` int(11) NOT NULL COMMENT '动作耗时',
  `err_msg` varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '错误描述',
  `tako_sn` varchar(50) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '外部存储tako编码',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  `feature_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '指标总量',
  `fee_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '计费量',
  PRIMARY KEY (`action_log_id`) USING BTREE,
  KEY `idx_product` (`product`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='动作记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `action_logs_202507`
--

DROP TABLE IF EXISTS `action_logs_202507`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `action_logs_202507` (
  `action_log_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `node_id` bigint(20) NOT NULL COMMENT '节点id',
  `tag` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '业务标记',
  `service_type` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '服务类型',
  `function` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '功能类型',
  `input_param` mediumtext COLLATE utf8mb4_bin COMMENT '调用参数',
  `output` mediumtext COLLATE utf8mb4_bin COMMENT '调用结果',
  `cost_time` int(11) NOT NULL COMMENT '动作耗时',
  `err_msg` varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '错误描述',
  `tako_sn` varchar(50) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '外部存储tako编码',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  `feature_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '指标总量',
  `fee_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '计费量',
  PRIMARY KEY (`action_log_id`) USING BTREE,
  KEY `idx_product` (`product`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='动作记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `action_logs_202508`
--

DROP TABLE IF EXISTS `action_logs_202508`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `action_logs_202508` (
  `action_log_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `node_id` bigint(20) NOT NULL COMMENT '节点id',
  `tag` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '业务标记',
  `service_type` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '服务类型',
  `function` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '功能类型',
  `input_param` mediumtext COLLATE utf8mb4_bin COMMENT '调用参数',
  `output` mediumtext COLLATE utf8mb4_bin COMMENT '调用结果',
  `cost_time` int(11) NOT NULL COMMENT '动作耗时',
  `err_msg` varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '错误描述',
  `tako_sn` varchar(50) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '外部存储tako编码',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  `feature_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '指标总量',
  `fee_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '计费量',
  PRIMARY KEY (`action_log_id`) USING BTREE,
  KEY `idx_product` (`product`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='动作记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `action_logs_202509`
--

DROP TABLE IF EXISTS `action_logs_202509`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `action_logs_202509` (
  `action_log_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `node_id` bigint(20) NOT NULL COMMENT '节点id',
  `tag` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '业务标记',
  `service_type` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '服务类型',
  `function` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '功能类型',
  `input_param` mediumtext COLLATE utf8mb4_bin COMMENT '调用参数',
  `output` mediumtext COLLATE utf8mb4_bin COMMENT '调用结果',
  `cost_time` int(11) NOT NULL COMMENT '动作耗时',
  `err_msg` varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '错误描述',
  `tako_sn` varchar(50) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '外部存储tako编码',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  `feature_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '指标总量',
  `fee_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '计费量',
  PRIMARY KEY (`action_log_id`) USING BTREE,
  KEY `idx_product` (`product`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='动作记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `action_logs_202510`
--

DROP TABLE IF EXISTS `action_logs_202510`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `action_logs_202510` (
  `action_log_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `node_id` bigint(20) NOT NULL COMMENT '节点id',
  `tag` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '业务标记',
  `service_type` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '服务类型',
  `function` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '功能类型',
  `input_param` mediumtext COLLATE utf8mb4_bin COMMENT '调用参数',
  `output` mediumtext COLLATE utf8mb4_bin COMMENT '调用结果',
  `cost_time` int(11) NOT NULL COMMENT '动作耗时',
  `err_msg` varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '错误描述',
  `tako_sn` varchar(50) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '外部存储tako编码',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  `feature_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '指标总量',
  `fee_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '计费量',
  PRIMARY KEY (`action_log_id`) USING BTREE,
  KEY `idx_product` (`product`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='动作记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `action_logs_202511`
--

DROP TABLE IF EXISTS `action_logs_202511`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `action_logs_202511` (
  `action_log_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `node_id` bigint(20) NOT NULL COMMENT '节点id',
  `tag` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '业务标记',
  `service_type` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '服务类型',
  `function` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '功能类型',
  `input_param` mediumtext COLLATE utf8mb4_bin COMMENT '调用参数',
  `output` mediumtext COLLATE utf8mb4_bin COMMENT '调用结果',
  `cost_time` int(11) NOT NULL COMMENT '动作耗时',
  `err_msg` varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '错误描述',
  `tako_sn` varchar(50) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '外部存储tako编码',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  `feature_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '指标总量',
  `fee_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '计费量',
  PRIMARY KEY (`action_log_id`) USING BTREE,
  KEY `idx_product` (`product`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='动作记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `action_logs_202512`
--

DROP TABLE IF EXISTS `action_logs_202512`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `action_logs_202512` (
  `action_log_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `node_id` bigint(20) NOT NULL COMMENT '节点id',
  `tag` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '业务标记',
  `service_type` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '服务类型',
  `function` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '功能类型',
  `input_param` mediumtext COLLATE utf8mb4_bin COMMENT '调用参数',
  `output` mediumtext COLLATE utf8mb4_bin COMMENT '调用结果',
  `cost_time` int(11) NOT NULL COMMENT '动作耗时',
  `err_msg` varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '错误描述',
  `tako_sn` varchar(50) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '外部存储tako编码',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  `feature_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '指标总量',
  `fee_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '计费量',
  PRIMARY KEY (`action_log_id`) USING BTREE,
  KEY `idx_product` (`product`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='动作记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `apply_requests_202209`
--

DROP TABLE IF EXISTS `apply_requests_202209`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `apply_requests_202209` (
  `apply_request_id` bigint(20) NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `task_type` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '任务类型',
  `flow_name` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT 'flow名',
  `apply_status` varchar(30) COLLATE utf8mb4_bin NOT NULL DEFAULT 'Init' COMMENT '申请状态',
  `product_config_id` bigint(20) NOT NULL COMMENT '产品配置id',
  `flow_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'FlowId',
  `start_time` datetime(3) NOT NULL COMMENT '开始时间',
  `end_time` datetime(3) DEFAULT NULL COMMENT '结束时间',
  `input_param` text COLLATE utf8mb4_bin COMMENT '输入参数',
  `check_result` text COLLATE utf8mb4_bin COMMENT '授信结果',
  `fail_reason` text COLLATE utf8mb4_bin COMMENT '异常信息',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  `flow_graph_snapshot_id` bigint(20) NOT NULL COMMENT 'flow graph 快照 id',
  PRIMARY KEY (`apply_request_id`) USING BTREE,
  UNIQUE KEY `uniq_workspace_apply_id` (`customer`,`apply_id`) USING BTREE,
  KEY `idx_product_tasktype` (`product`,`task_type`) USING BTREE,
  KEY `idx_status` (`apply_status`) USING BTREE,
  KEY `idx_created_at` (`created_at`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE,
  KEY `idx_applydate` (`apply_date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='决策申请';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `apply_requests_202210`
--

DROP TABLE IF EXISTS `apply_requests_202210`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `apply_requests_202210` (
  `apply_request_id` bigint(20) NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `task_type` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '任务类型',
  `flow_name` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT 'flow名',
  `apply_status` varchar(30) COLLATE utf8mb4_bin NOT NULL DEFAULT 'Init' COMMENT '申请状态',
  `product_config_id` bigint(20) NOT NULL COMMENT '产品配置id',
  `flow_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'FlowId',
  `start_time` datetime(3) NOT NULL COMMENT '开始时间',
  `end_time` datetime(3) DEFAULT NULL COMMENT '结束时间',
  `input_param` text COLLATE utf8mb4_bin COMMENT '输入参数',
  `check_result` text COLLATE utf8mb4_bin COMMENT '授信结果',
  `fail_reason` text COLLATE utf8mb4_bin COMMENT '异常信息',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  `flow_graph_snapshot_id` bigint(20) NOT NULL COMMENT 'flow graph 快照 id',
  PRIMARY KEY (`apply_request_id`) USING BTREE,
  UNIQUE KEY `uniq_workspace_apply_id` (`customer`,`apply_id`) USING BTREE,
  KEY `idx_product_tasktype` (`product`,`task_type`) USING BTREE,
  KEY `idx_status` (`apply_status`) USING BTREE,
  KEY `idx_created_at` (`created_at`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE,
  KEY `idx_applydate` (`apply_date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='决策申请';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `apply_requests_202503`
--

DROP TABLE IF EXISTS `apply_requests_202503`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `apply_requests_202503` (
  `apply_request_id` bigint(20) NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `task_type` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '任务类型',
  `flow_name` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT 'flow名',
  `apply_status` varchar(30) COLLATE utf8mb4_bin NOT NULL DEFAULT 'Init' COMMENT '申请状态',
  `product_config_id` bigint(20) NOT NULL COMMENT '产品配置id',
  `flow_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'FlowId',
  `start_time` datetime(3) NOT NULL COMMENT '开始时间',
  `end_time` datetime(3) DEFAULT NULL COMMENT '结束时间',
  `input_param` text COLLATE utf8mb4_bin COMMENT '输入参数',
  `check_result` mediumtext COLLATE utf8mb4_bin COMMENT '授信结果',
  `fail_reason` text COLLATE utf8mb4_bin COMMENT '异常信息',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  `flow_graph_snapshot_id` bigint(20) NOT NULL COMMENT 'flow graph 快照 id',
  PRIMARY KEY (`apply_request_id`) USING BTREE,
  UNIQUE KEY `uniq_workspace_apply_id` (`customer`,`apply_id`) USING BTREE,
  KEY `idx_product_tasktype` (`product`,`task_type`) USING BTREE,
  KEY `idx_status` (`apply_status`) USING BTREE,
  KEY `idx_created_at` (`created_at`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE,
  KEY `idx_applydate` (`apply_date`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='决策申请';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `apply_requests_202504`
--

DROP TABLE IF EXISTS `apply_requests_202504`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `apply_requests_202504` (
  `apply_request_id` bigint(20) NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `task_type` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '任务类型',
  `flow_name` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT 'flow名',
  `apply_status` varchar(30) COLLATE utf8mb4_bin NOT NULL DEFAULT 'Init' COMMENT '申请状态',
  `product_config_id` bigint(20) NOT NULL COMMENT '产品配置id',
  `flow_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'FlowId',
  `start_time` datetime(3) NOT NULL COMMENT '开始时间',
  `end_time` datetime(3) DEFAULT NULL COMMENT '结束时间',
  `input_param` text COLLATE utf8mb4_bin COMMENT '输入参数',
  `check_result` mediumtext COLLATE utf8mb4_bin COMMENT '授信结果',
  `fail_reason` text COLLATE utf8mb4_bin COMMENT '异常信息',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  `flow_graph_snapshot_id` bigint(20) NOT NULL COMMENT 'flow graph 快照 id',
  PRIMARY KEY (`apply_request_id`) USING BTREE,
  UNIQUE KEY `uniq_workspace_apply_id` (`customer`,`apply_id`) USING BTREE,
  KEY `idx_product_tasktype` (`product`,`task_type`) USING BTREE,
  KEY `idx_status` (`apply_status`) USING BTREE,
  KEY `idx_created_at` (`created_at`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE,
  KEY `idx_applydate` (`apply_date`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='决策申请';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `apply_requests_202505`
--

DROP TABLE IF EXISTS `apply_requests_202505`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `apply_requests_202505` (
  `apply_request_id` bigint(20) NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `task_type` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '任务类型',
  `flow_name` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT 'flow名',
  `apply_status` varchar(30) COLLATE utf8mb4_bin NOT NULL DEFAULT 'Init' COMMENT '申请状态',
  `product_config_id` bigint(20) NOT NULL COMMENT '产品配置id',
  `flow_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'FlowId',
  `start_time` datetime(3) NOT NULL COMMENT '开始时间',
  `end_time` datetime(3) DEFAULT NULL COMMENT '结束时间',
  `input_param` text COLLATE utf8mb4_bin COMMENT '输入参数',
  `check_result` mediumtext COLLATE utf8mb4_bin COMMENT '授信结果',
  `fail_reason` text COLLATE utf8mb4_bin COMMENT '异常信息',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  `flow_graph_snapshot_id` bigint(20) NOT NULL COMMENT 'flow graph 快照 id',
  PRIMARY KEY (`apply_request_id`) USING BTREE,
  UNIQUE KEY `uniq_workspace_apply_id` (`customer`,`apply_id`) USING BTREE,
  KEY `idx_product_tasktype` (`product`,`task_type`) USING BTREE,
  KEY `idx_status` (`apply_status`) USING BTREE,
  KEY `idx_created_at` (`created_at`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE,
  KEY `idx_applydate` (`apply_date`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='决策申请';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `apply_requests_202506`
--

DROP TABLE IF EXISTS `apply_requests_202506`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `apply_requests_202506` (
  `apply_request_id` bigint(20) NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `task_type` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '任务类型',
  `flow_name` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT 'flow名',
  `apply_status` varchar(30) COLLATE utf8mb4_bin NOT NULL DEFAULT 'Init' COMMENT '申请状态',
  `product_config_id` bigint(20) NOT NULL COMMENT '产品配置id',
  `flow_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'FlowId',
  `start_time` datetime(3) NOT NULL COMMENT '开始时间',
  `end_time` datetime(3) DEFAULT NULL COMMENT '结束时间',
  `input_param` text COLLATE utf8mb4_bin COMMENT '输入参数',
  `check_result` mediumtext COLLATE utf8mb4_bin COMMENT '授信结果',
  `fail_reason` text COLLATE utf8mb4_bin COMMENT '异常信息',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  `flow_graph_snapshot_id` bigint(20) NOT NULL COMMENT 'flow graph 快照 id',
  PRIMARY KEY (`apply_request_id`) USING BTREE,
  UNIQUE KEY `uniq_workspace_apply_id` (`customer`,`apply_id`) USING BTREE,
  KEY `idx_product_tasktype` (`product`,`task_type`) USING BTREE,
  KEY `idx_status` (`apply_status`) USING BTREE,
  KEY `idx_created_at` (`created_at`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE,
  KEY `idx_applydate` (`apply_date`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='决策申请';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `apply_requests_202507`
--

DROP TABLE IF EXISTS `apply_requests_202507`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `apply_requests_202507` (
  `apply_request_id` bigint(20) NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `task_type` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '任务类型',
  `flow_name` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT 'flow名',
  `apply_status` varchar(30) COLLATE utf8mb4_bin NOT NULL DEFAULT 'Init' COMMENT '申请状态',
  `product_config_id` bigint(20) NOT NULL COMMENT '产品配置id',
  `flow_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'FlowId',
  `start_time` datetime(3) NOT NULL COMMENT '开始时间',
  `end_time` datetime(3) DEFAULT NULL COMMENT '结束时间',
  `input_param` text COLLATE utf8mb4_bin COMMENT '输入参数',
  `check_result` mediumtext COLLATE utf8mb4_bin COMMENT '授信结果',
  `fail_reason` text COLLATE utf8mb4_bin COMMENT '异常信息',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  `flow_graph_snapshot_id` bigint(20) NOT NULL COMMENT 'flow graph 快照 id',
  PRIMARY KEY (`apply_request_id`) USING BTREE,
  UNIQUE KEY `uniq_workspace_apply_id` (`customer`,`apply_id`) USING BTREE,
  KEY `idx_product_tasktype` (`product`,`task_type`) USING BTREE,
  KEY `idx_status` (`apply_status`) USING BTREE,
  KEY `idx_created_at` (`created_at`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE,
  KEY `idx_applydate` (`apply_date`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='决策申请';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `apply_requests_202508`
--

DROP TABLE IF EXISTS `apply_requests_202508`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `apply_requests_202508` (
  `apply_request_id` bigint(20) NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `task_type` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '任务类型',
  `flow_name` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT 'flow名',
  `apply_status` varchar(30) COLLATE utf8mb4_bin NOT NULL DEFAULT 'Init' COMMENT '申请状态',
  `product_config_id` bigint(20) NOT NULL COMMENT '产品配置id',
  `flow_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'FlowId',
  `start_time` datetime(3) NOT NULL COMMENT '开始时间',
  `end_time` datetime(3) DEFAULT NULL COMMENT '结束时间',
  `input_param` text COLLATE utf8mb4_bin COMMENT '输入参数',
  `check_result` mediumtext COLLATE utf8mb4_bin COMMENT '授信结果',
  `fail_reason` text COLLATE utf8mb4_bin COMMENT '异常信息',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  `flow_graph_snapshot_id` bigint(20) NOT NULL COMMENT 'flow graph 快照 id',
  PRIMARY KEY (`apply_request_id`) USING BTREE,
  UNIQUE KEY `uniq_workspace_apply_id` (`customer`,`apply_id`) USING BTREE,
  KEY `idx_product_tasktype` (`product`,`task_type`) USING BTREE,
  KEY `idx_status` (`apply_status`) USING BTREE,
  KEY `idx_created_at` (`created_at`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE,
  KEY `idx_applydate` (`apply_date`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='决策申请';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `apply_requests_202509`
--

DROP TABLE IF EXISTS `apply_requests_202509`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `apply_requests_202509` (
  `apply_request_id` bigint(20) NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `task_type` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '任务类型',
  `flow_name` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT 'flow名',
  `apply_status` varchar(30) COLLATE utf8mb4_bin NOT NULL DEFAULT 'Init' COMMENT '申请状态',
  `product_config_id` bigint(20) NOT NULL COMMENT '产品配置id',
  `flow_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'FlowId',
  `start_time` datetime(3) NOT NULL COMMENT '开始时间',
  `end_time` datetime(3) DEFAULT NULL COMMENT '结束时间',
  `input_param` text COLLATE utf8mb4_bin COMMENT '输入参数',
  `check_result` mediumtext COLLATE utf8mb4_bin COMMENT '授信结果',
  `fail_reason` text COLLATE utf8mb4_bin COMMENT '异常信息',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  `flow_graph_snapshot_id` bigint(20) NOT NULL COMMENT 'flow graph 快照 id',
  PRIMARY KEY (`apply_request_id`) USING BTREE,
  UNIQUE KEY `uniq_workspace_apply_id` (`customer`,`apply_id`) USING BTREE,
  KEY `idx_product_tasktype` (`product`,`task_type`) USING BTREE,
  KEY `idx_status` (`apply_status`) USING BTREE,
  KEY `idx_created_at` (`created_at`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE,
  KEY `idx_applydate` (`apply_date`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='决策申请';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `apply_requests_202510`
--

DROP TABLE IF EXISTS `apply_requests_202510`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `apply_requests_202510` (
  `apply_request_id` bigint(20) NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `task_type` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '任务类型',
  `flow_name` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT 'flow名',
  `apply_status` varchar(30) COLLATE utf8mb4_bin NOT NULL DEFAULT 'Init' COMMENT '申请状态',
  `product_config_id` bigint(20) NOT NULL COMMENT '产品配置id',
  `flow_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'FlowId',
  `start_time` datetime(3) NOT NULL COMMENT '开始时间',
  `end_time` datetime(3) DEFAULT NULL COMMENT '结束时间',
  `input_param` text COLLATE utf8mb4_bin COMMENT '输入参数',
  `check_result` mediumtext COLLATE utf8mb4_bin COMMENT '授信结果',
  `fail_reason` text COLLATE utf8mb4_bin COMMENT '异常信息',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  `flow_graph_snapshot_id` bigint(20) NOT NULL COMMENT 'flow graph 快照 id',
  PRIMARY KEY (`apply_request_id`) USING BTREE,
  UNIQUE KEY `uniq_workspace_apply_id` (`customer`,`apply_id`) USING BTREE,
  KEY `idx_product_tasktype` (`product`,`task_type`) USING BTREE,
  KEY `idx_status` (`apply_status`) USING BTREE,
  KEY `idx_created_at` (`created_at`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE,
  KEY `idx_applydate` (`apply_date`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='决策申请';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `apply_requests_202511`
--

DROP TABLE IF EXISTS `apply_requests_202511`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `apply_requests_202511` (
  `apply_request_id` bigint(20) NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `task_type` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '任务类型',
  `flow_name` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT 'flow名',
  `apply_status` varchar(30) COLLATE utf8mb4_bin NOT NULL DEFAULT 'Init' COMMENT '申请状态',
  `product_config_id` bigint(20) NOT NULL COMMENT '产品配置id',
  `flow_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'FlowId',
  `start_time` datetime(3) NOT NULL COMMENT '开始时间',
  `end_time` datetime(3) DEFAULT NULL COMMENT '结束时间',
  `input_param` text COLLATE utf8mb4_bin COMMENT '输入参数',
  `check_result` mediumtext COLLATE utf8mb4_bin COMMENT '授信结果',
  `fail_reason` text COLLATE utf8mb4_bin COMMENT '异常信息',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  `flow_graph_snapshot_id` bigint(20) NOT NULL COMMENT 'flow graph 快照 id',
  PRIMARY KEY (`apply_request_id`) USING BTREE,
  UNIQUE KEY `uniq_workspace_apply_id` (`customer`,`apply_id`) USING BTREE,
  KEY `idx_product_tasktype` (`product`,`task_type`) USING BTREE,
  KEY `idx_status` (`apply_status`) USING BTREE,
  KEY `idx_created_at` (`created_at`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE,
  KEY `idx_applydate` (`apply_date`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='决策申请';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `apply_requests_202512`
--

DROP TABLE IF EXISTS `apply_requests_202512`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `apply_requests_202512` (
  `apply_request_id` bigint(20) NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `task_type` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '任务类型',
  `flow_name` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT 'flow名',
  `apply_status` varchar(30) COLLATE utf8mb4_bin NOT NULL DEFAULT 'Init' COMMENT '申请状态',
  `product_config_id` bigint(20) NOT NULL COMMENT '产品配置id',
  `flow_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'FlowId',
  `start_time` datetime(3) NOT NULL COMMENT '开始时间',
  `end_time` datetime(3) DEFAULT NULL COMMENT '结束时间',
  `input_param` text COLLATE utf8mb4_bin COMMENT '输入参数',
  `check_result` mediumtext COLLATE utf8mb4_bin COMMENT '授信结果',
  `fail_reason` text COLLATE utf8mb4_bin COMMENT '异常信息',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  `flow_graph_snapshot_id` bigint(20) NOT NULL COMMENT 'flow graph 快照 id',
  PRIMARY KEY (`apply_request_id`) USING BTREE,
  UNIQUE KEY `uniq_workspace_apply_id` (`customer`,`apply_id`) USING BTREE,
  KEY `idx_product_tasktype` (`product`,`task_type`) USING BTREE,
  KEY `idx_status` (`apply_status`) USING BTREE,
  KEY `idx_created_at` (`created_at`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE,
  KEY `idx_applydate` (`apply_date`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='决策申请';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `audit_log`
--

DROP TABLE IF EXISTS `audit_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `audit_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `entity_type` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '操作实体类型',
  `entity_id` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '操作实体标识',
  `entity_operation` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '操作',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `created_by` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '操作人',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_entity_id` (`entity_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='系统审计日志';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `batch_run_details`
--

DROP TABLE IF EXISTS `batch_run_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `batch_run_details` (
  `id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '主键',
  `batch_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'batch_run_sample表主键',
  `run_status` int(11) NOT NULL DEFAULT '0' COMMENT '状态，初始，进行中，完成',
  `i_trade_no` varchar(50) COLLATE utf8mb4_bin NOT NULL DEFAULT '0' COMMENT '内部流水号',
  `o_trade_no` varchar(50) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '外部流水号',
  `msg` varchar(100) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'msg',
  PRIMARY KEY (`id`),
  KEY `idx_bid` (`batch_id`),
  KEY `idx_rs` (`run_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='批量测试明细';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `batch_run_details_data`
--

DROP TABLE IF EXISTS `batch_run_details_data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `batch_run_details_data` (
  `id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '主键',
  `req_data` text COLLATE utf8mb4_bin NOT NULL COMMENT '数据',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='批量测试明细数据';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `batch_run_sample`
--

DROP TABLE IF EXISTS `batch_run_sample`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `batch_run_sample` (
  `id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '主键',
  `product` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '产品编码',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '用户编码',
  `sampleq` int(11) NOT NULL DEFAULT '0' COMMENT '样本数量',
  `resultq` int(11) NOT NULL DEFAULT '0' COMMENT '有结果的样本数量',
  `percent` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '进度',
  `result` int(11) NOT NULL DEFAULT '1' COMMENT '状态',
  `file_name` varchar(100) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '文件名称',
  `cols` varchar(255) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'excel表头',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_ct` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='批量测试';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `context_aggregation_202209`
--

DROP TABLE IF EXISTS `context_aggregation_202209`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `context_aggregation_202209` (
  `context_aggregation_id` bigint(20) NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `flow_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'jingway运行时的flowId',
  `aggregation` mediumtext COLLATE utf8mb4_bin COMMENT '聚合完成之后的数据，以json->map->map形式组织',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '处理状态 0 待处理 1 处理完毕',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`context_aggregation_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_create_at` (`create_at`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='决策申请运行后上下文变量聚合表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `context_aggregation_202210`
--

DROP TABLE IF EXISTS `context_aggregation_202210`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `context_aggregation_202210` (
  `context_aggregation_id` bigint(20) NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `flow_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'jingway运行时的flowId',
  `aggregation` mediumtext COLLATE utf8mb4_bin COMMENT '聚合完成之后的数据，以json->map->map形式组织',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '处理状态 0 待处理 1 处理完毕',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`context_aggregation_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_create_at` (`create_at`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='决策申请运行后上下文变量聚合表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `context_aggregation_202503`
--

DROP TABLE IF EXISTS `context_aggregation_202503`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `context_aggregation_202503` (
  `context_aggregation_id` bigint(20) NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `flow_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'jingway运行时的flowId',
  `aggregation` mediumtext COLLATE utf8mb4_bin COMMENT '聚合完成之后的数据，以json->map->map形式组织',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '处理状态 0 待处理 1 处理完毕',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`context_aggregation_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_create_at` (`create_at`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='决策申请运行后上下文变量聚合表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `context_aggregation_202504`
--

DROP TABLE IF EXISTS `context_aggregation_202504`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `context_aggregation_202504` (
  `context_aggregation_id` bigint(20) NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `flow_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'jingway运行时的flowId',
  `aggregation` mediumtext COLLATE utf8mb4_bin COMMENT '聚合完成之后的数据，以json->map->map形式组织',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '处理状态 0 待处理 1 处理完毕',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`context_aggregation_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_create_at` (`create_at`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='决策申请运行后上下文变量聚合表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `context_aggregation_202505`
--

DROP TABLE IF EXISTS `context_aggregation_202505`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `context_aggregation_202505` (
  `context_aggregation_id` bigint(20) NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `flow_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'jingway运行时的flowId',
  `aggregation` mediumtext COLLATE utf8mb4_bin COMMENT '聚合完成之后的数据，以json->map->map形式组织',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '处理状态 0 待处理 1 处理完毕',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`context_aggregation_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_create_at` (`create_at`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='决策申请运行后上下文变量聚合表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `context_aggregation_202506`
--

DROP TABLE IF EXISTS `context_aggregation_202506`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `context_aggregation_202506` (
  `context_aggregation_id` bigint(20) NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `flow_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'jingway运行时的flowId',
  `aggregation` mediumtext COLLATE utf8mb4_bin COMMENT '聚合完成之后的数据，以json->map->map形式组织',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '处理状态 0 待处理 1 处理完毕',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`context_aggregation_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_create_at` (`create_at`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='决策申请运行后上下文变量聚合表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `context_aggregation_202507`
--

DROP TABLE IF EXISTS `context_aggregation_202507`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `context_aggregation_202507` (
  `context_aggregation_id` bigint(20) NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `flow_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'jingway运行时的flowId',
  `aggregation` mediumtext COLLATE utf8mb4_bin COMMENT '聚合完成之后的数据，以json->map->map形式组织',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '处理状态 0 待处理 1 处理完毕',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`context_aggregation_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_create_at` (`create_at`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='决策申请运行后上下文变量聚合表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `context_aggregation_202508`
--

DROP TABLE IF EXISTS `context_aggregation_202508`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `context_aggregation_202508` (
  `context_aggregation_id` bigint(20) NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `flow_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'jingway运行时的flowId',
  `aggregation` mediumtext COLLATE utf8mb4_bin COMMENT '聚合完成之后的数据，以json->map->map形式组织',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '处理状态 0 待处理 1 处理完毕',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`context_aggregation_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_create_at` (`create_at`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='决策申请运行后上下文变量聚合表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `context_aggregation_202509`
--

DROP TABLE IF EXISTS `context_aggregation_202509`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `context_aggregation_202509` (
  `context_aggregation_id` bigint(20) NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `flow_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'jingway运行时的flowId',
  `aggregation` mediumtext COLLATE utf8mb4_bin COMMENT '聚合完成之后的数据，以json->map->map形式组织',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '处理状态 0 待处理 1 处理完毕',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`context_aggregation_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_create_at` (`create_at`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='决策申请运行后上下文变量聚合表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `context_aggregation_202510`
--

DROP TABLE IF EXISTS `context_aggregation_202510`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `context_aggregation_202510` (
  `context_aggregation_id` bigint(20) NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `flow_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'jingway运行时的flowId',
  `aggregation` mediumtext COLLATE utf8mb4_bin COMMENT '聚合完成之后的数据，以json->map->map形式组织',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '处理状态 0 待处理 1 处理完毕',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`context_aggregation_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_create_at` (`create_at`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='决策申请运行后上下文变量聚合表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `context_aggregation_202511`
--

DROP TABLE IF EXISTS `context_aggregation_202511`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `context_aggregation_202511` (
  `context_aggregation_id` bigint(20) NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `flow_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'jingway运行时的flowId',
  `aggregation` mediumtext COLLATE utf8mb4_bin COMMENT '聚合完成之后的数据，以json->map->map形式组织',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '处理状态 0 待处理 1 处理完毕',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`context_aggregation_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_create_at` (`create_at`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='决策申请运行后上下文变量聚合表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `context_aggregation_202512`
--

DROP TABLE IF EXISTS `context_aggregation_202512`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `context_aggregation_202512` (
  `context_aggregation_id` bigint(20) NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `flow_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'jingway运行时的flowId',
  `aggregation` mediumtext COLLATE utf8mb4_bin COMMENT '聚合完成之后的数据，以json->map->map形式组织',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '处理状态 0 待处理 1 处理完毕',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`context_aggregation_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_create_at` (`create_at`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='决策申请运行后上下文变量聚合表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `context_detail_202209`
--

DROP TABLE IF EXISTS `context_detail_202209`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `context_detail_202209` (
  `context_detail_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `flow_id` bigint(20) NOT NULL COMMENT '运行时flowid',
  `node_id` bigint(20) NOT NULL COMMENT '运行时nodeid',
  `tag` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '业务标记',
  `service_key` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '服务属性',
  `context_key` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '上下文属性',
  `data_type` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '数据类型',
  `value` text COLLATE utf8mb4_bin COMMENT '原值',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`context_detail_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_flow_node` (`flow_id`,`node_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='运行上下文';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `context_detail_202210`
--

DROP TABLE IF EXISTS `context_detail_202210`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `context_detail_202210` (
  `context_detail_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `flow_id` bigint(20) NOT NULL COMMENT '运行时flowid',
  `node_id` bigint(20) NOT NULL COMMENT '运行时nodeid',
  `tag` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '业务标记',
  `service_key` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '服务属性',
  `context_key` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '上下文属性',
  `data_type` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '数据类型',
  `value` text COLLATE utf8mb4_bin COMMENT '原值',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`context_detail_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_flow_node` (`flow_id`,`node_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='运行上下文';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `context_detail_202503`
--

DROP TABLE IF EXISTS `context_detail_202503`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `context_detail_202503` (
  `context_detail_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `flow_id` bigint(20) NOT NULL COMMENT '运行时flowid',
  `node_id` bigint(20) NOT NULL COMMENT '运行时nodeid',
  `tag` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '业务标记',
  `service_key` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '服务属性',
  `context_key` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '上下文属性',
  `data_type` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '数据类型',
  `value` mediumtext COLLATE utf8mb4_bin COMMENT '原值',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`context_detail_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_flow_node` (`flow_id`,`node_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='运行上下文';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `context_detail_202504`
--

DROP TABLE IF EXISTS `context_detail_202504`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `context_detail_202504` (
  `context_detail_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `flow_id` bigint(20) NOT NULL COMMENT '运行时flowid',
  `node_id` bigint(20) NOT NULL COMMENT '运行时nodeid',
  `tag` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '业务标记',
  `service_key` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '服务属性',
  `context_key` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '上下文属性',
  `data_type` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '数据类型',
  `value` mediumtext COLLATE utf8mb4_bin COMMENT '原值',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`context_detail_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_flow_node` (`flow_id`,`node_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='运行上下文';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `context_detail_202505`
--

DROP TABLE IF EXISTS `context_detail_202505`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `context_detail_202505` (
  `context_detail_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `flow_id` bigint(20) NOT NULL COMMENT '运行时flowid',
  `node_id` bigint(20) NOT NULL COMMENT '运行时nodeid',
  `tag` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '业务标记',
  `service_key` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '服务属性',
  `context_key` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '上下文属性',
  `data_type` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '数据类型',
  `value` mediumtext COLLATE utf8mb4_bin COMMENT '原值',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`context_detail_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_flow_node` (`flow_id`,`node_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='运行上下文';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `context_detail_202506`
--

DROP TABLE IF EXISTS `context_detail_202506`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `context_detail_202506` (
  `context_detail_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `flow_id` bigint(20) NOT NULL COMMENT '运行时flowid',
  `node_id` bigint(20) NOT NULL COMMENT '运行时nodeid',
  `tag` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '业务标记',
  `service_key` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '服务属性',
  `context_key` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '上下文属性',
  `data_type` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '数据类型',
  `value` mediumtext COLLATE utf8mb4_bin COMMENT '原值',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`context_detail_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_flow_node` (`flow_id`,`node_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='运行上下文';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `context_detail_202507`
--

DROP TABLE IF EXISTS `context_detail_202507`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `context_detail_202507` (
  `context_detail_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `flow_id` bigint(20) NOT NULL COMMENT '运行时flowid',
  `node_id` bigint(20) NOT NULL COMMENT '运行时nodeid',
  `tag` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '业务标记',
  `service_key` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '服务属性',
  `context_key` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '上下文属性',
  `data_type` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '数据类型',
  `value` mediumtext COLLATE utf8mb4_bin COMMENT '原值',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`context_detail_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_flow_node` (`flow_id`,`node_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='运行上下文';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `context_detail_202508`
--

DROP TABLE IF EXISTS `context_detail_202508`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `context_detail_202508` (
  `context_detail_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `flow_id` bigint(20) NOT NULL COMMENT '运行时flowid',
  `node_id` bigint(20) NOT NULL COMMENT '运行时nodeid',
  `tag` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '业务标记',
  `service_key` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '服务属性',
  `context_key` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '上下文属性',
  `data_type` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '数据类型',
  `value` mediumtext COLLATE utf8mb4_bin COMMENT '原值',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`context_detail_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_flow_node` (`flow_id`,`node_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='运行上下文';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `context_detail_202509`
--

DROP TABLE IF EXISTS `context_detail_202509`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `context_detail_202509` (
  `context_detail_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `flow_id` bigint(20) NOT NULL COMMENT '运行时flowid',
  `node_id` bigint(20) NOT NULL COMMENT '运行时nodeid',
  `tag` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '业务标记',
  `service_key` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '服务属性',
  `context_key` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '上下文属性',
  `data_type` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '数据类型',
  `value` mediumtext COLLATE utf8mb4_bin COMMENT '原值',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`context_detail_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_flow_node` (`flow_id`,`node_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='运行上下文';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `context_detail_202510`
--

DROP TABLE IF EXISTS `context_detail_202510`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `context_detail_202510` (
  `context_detail_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `flow_id` bigint(20) NOT NULL COMMENT '运行时flowid',
  `node_id` bigint(20) NOT NULL COMMENT '运行时nodeid',
  `tag` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '业务标记',
  `service_key` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '服务属性',
  `context_key` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '上下文属性',
  `data_type` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '数据类型',
  `value` mediumtext COLLATE utf8mb4_bin COMMENT '原值',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`context_detail_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_flow_node` (`flow_id`,`node_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='运行上下文';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `context_detail_202511`
--

DROP TABLE IF EXISTS `context_detail_202511`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `context_detail_202511` (
  `context_detail_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `flow_id` bigint(20) NOT NULL COMMENT '运行时flowid',
  `node_id` bigint(20) NOT NULL COMMENT '运行时nodeid',
  `tag` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '业务标记',
  `service_key` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '服务属性',
  `context_key` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '上下文属性',
  `data_type` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '数据类型',
  `value` mediumtext COLLATE utf8mb4_bin COMMENT '原值',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`context_detail_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_flow_node` (`flow_id`,`node_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='运行上下文';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `context_detail_202512`
--

DROP TABLE IF EXISTS `context_detail_202512`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `context_detail_202512` (
  `context_detail_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `flow_id` bigint(20) NOT NULL COMMENT '运行时flowid',
  `node_id` bigint(20) NOT NULL COMMENT '运行时nodeid',
  `tag` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '业务标记',
  `service_key` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '服务属性',
  `context_key` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '上下文属性',
  `data_type` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '数据类型',
  `value` mediumtext COLLATE utf8mb4_bin COMMENT '原值',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`context_detail_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_flow_node` (`flow_id`,`node_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='运行上下文';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `customer_info`
--

DROP TABLE IF EXISTS `customer_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `customer_info` (
  `id` bigint(20) unsigned NOT NULL COMMENT '主键ID',
  `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '编码',
  `name` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '简称',
  `full_name` varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '客户全称',
  `customer_status` smallint(6) NOT NULL DEFAULT '2' COMMENT '状态',
  `parent_code` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '父客户编码',
  `customer_type` tinyint(3) NOT NULL DEFAULT '0' COMMENT '客户类型',
  `route_key` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '路由key',
  `route` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否路由子客户',
  `email` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '邮箱地址',
  `app_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'app_id',
  `aes_key` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'aes_key',
  `secret_key` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'secret_key',
  `token` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'token',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uniq_code` (`code`) USING BTREE,
  UNIQUE KEY `uniq_appid` (`app_id`) USING BTREE,
  KEY `idx_ct` (`create_at`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='客户信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `delay_event`
--

DROP TABLE IF EXISTS `delay_event`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `delay_event` (
  `delay_event_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `inner_apply_id` bigint(20) unsigned NOT NULL COMMENT '申请id',
  `node_uuid` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '节点uuid',
  `flow_id` bigint(20) unsigned NOT NULL COMMENT 'flow配置模板id',
  `flow_name` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT 'flow名称',
  `status` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '状态',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`delay_event_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_start_time` (`start_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='延迟事件';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `flow_202209`
--

DROP TABLE IF EXISTS `flow_202209`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `flow_202209` (
  `flow_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `flow_config_id` bigint(20) unsigned NOT NULL COMMENT 'flow配置模板id',
  `flow_config_name` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT 'flow配置名称',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `status` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '状态',
  `stop_reason` varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '停止原因',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`flow_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='决策流';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `flow_202210`
--

DROP TABLE IF EXISTS `flow_202210`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `flow_202210` (
  `flow_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `flow_config_id` bigint(20) unsigned NOT NULL COMMENT 'flow配置模板id',
  `flow_config_name` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT 'flow配置名称',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `status` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '状态',
  `stop_reason` varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '停止原因',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`flow_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='决策流';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `flow_202503`
--

DROP TABLE IF EXISTS `flow_202503`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `flow_202503` (
  `flow_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `flow_config_id` bigint(20) unsigned NOT NULL COMMENT 'flow配置模板id',
  `flow_config_name` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT 'flow配置名称',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `status` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '状态',
  `stop_reason` varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '停止原因',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`flow_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='决策流';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `flow_202504`
--

DROP TABLE IF EXISTS `flow_202504`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `flow_202504` (
  `flow_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `flow_config_id` bigint(20) unsigned NOT NULL COMMENT 'flow配置模板id',
  `flow_config_name` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT 'flow配置名称',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `status` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '状态',
  `stop_reason` varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '停止原因',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`flow_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='决策流';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `flow_202505`
--

DROP TABLE IF EXISTS `flow_202505`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `flow_202505` (
  `flow_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `flow_config_id` bigint(20) unsigned NOT NULL COMMENT 'flow配置模板id',
  `flow_config_name` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT 'flow配置名称',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `status` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '状态',
  `stop_reason` varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '停止原因',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`flow_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='决策流';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `flow_202506`
--

DROP TABLE IF EXISTS `flow_202506`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `flow_202506` (
  `flow_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `flow_config_id` bigint(20) unsigned NOT NULL COMMENT 'flow配置模板id',
  `flow_config_name` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT 'flow配置名称',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `status` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '状态',
  `stop_reason` varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '停止原因',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`flow_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='决策流';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `flow_202507`
--

DROP TABLE IF EXISTS `flow_202507`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `flow_202507` (
  `flow_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `flow_config_id` bigint(20) unsigned NOT NULL COMMENT 'flow配置模板id',
  `flow_config_name` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT 'flow配置名称',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `status` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '状态',
  `stop_reason` varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '停止原因',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`flow_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='决策流';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `flow_202508`
--

DROP TABLE IF EXISTS `flow_202508`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `flow_202508` (
  `flow_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `flow_config_id` bigint(20) unsigned NOT NULL COMMENT 'flow配置模板id',
  `flow_config_name` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT 'flow配置名称',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `status` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '状态',
  `stop_reason` varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '停止原因',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`flow_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='决策流';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `flow_202509`
--

DROP TABLE IF EXISTS `flow_202509`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `flow_202509` (
  `flow_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `flow_config_id` bigint(20) unsigned NOT NULL COMMENT 'flow配置模板id',
  `flow_config_name` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT 'flow配置名称',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `status` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '状态',
  `stop_reason` varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '停止原因',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`flow_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='决策流';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `flow_202510`
--

DROP TABLE IF EXISTS `flow_202510`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `flow_202510` (
  `flow_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `flow_config_id` bigint(20) unsigned NOT NULL COMMENT 'flow配置模板id',
  `flow_config_name` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT 'flow配置名称',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `status` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '状态',
  `stop_reason` varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '停止原因',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`flow_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='决策流';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `flow_202511`
--

DROP TABLE IF EXISTS `flow_202511`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `flow_202511` (
  `flow_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `flow_config_id` bigint(20) unsigned NOT NULL COMMENT 'flow配置模板id',
  `flow_config_name` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT 'flow配置名称',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `status` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '状态',
  `stop_reason` varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '停止原因',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`flow_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='决策流';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `flow_202512`
--

DROP TABLE IF EXISTS `flow_202512`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `flow_202512` (
  `flow_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `flow_config_id` bigint(20) unsigned NOT NULL COMMENT 'flow配置模板id',
  `flow_config_name` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT 'flow配置名称',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `status` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '状态',
  `stop_reason` varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '停止原因',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`flow_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='决策流';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `flow_graph_snapshot`
--

DROP TABLE IF EXISTS `flow_graph_snapshot`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `flow_graph_snapshot` (
  `flow_graph_snapshot_id` bigint(20) NOT NULL COMMENT '主键',
  `flow_name` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT 'mFlow 中 flow 编码',
  `flow_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'mFlow 中 flowId',
  `flow_graph` longtext COLLATE utf8mb4_bin COMMENT '流程图具体内容',
  `flow_sign` varchar(255) COLLATE utf8mb4_bin NOT NULL DEFAULT '0' COMMENT 'flow 唯一性签名',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`flow_graph_snapshot_id`) USING BTREE,
  KEY `idx_flow_name` (`flow_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='决策申请运行时flow快照';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mf_flow_config`
--

DROP TABLE IF EXISTS `mf_flow_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `mf_flow_config` (
  `flow_config_id` bigint(20) NOT NULL COMMENT 'FLOW配置ID',
  `parent_id` bigint(20) DEFAULT NULL COMMENT '根FLowConfigId',
  `name` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT 'FLOW名',
  `description` varchar(200) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '描述',
  `state` char(2) COLLATE utf8mb4_bin NOT NULL COMMENT 'FLOW配置状态',
  `ret_dft_val_when_fail` tinyint(1) NOT NULL COMMENT '失败时是否返回默认值',
  `creator` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modifier` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '修改人',
  `modified_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`flow_config_id`) USING BTREE,
  UNIQUE KEY `uniq_name` (`name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='FLOW配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mf_flow_publish_log`
--

DROP TABLE IF EXISTS `mf_flow_publish_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `mf_flow_publish_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `flow_config_id` bigint(20) NOT NULL COMMENT 'flow ID',
  `parent_id` bigint(20) NOT NULL COMMENT '根flowid',
  `publish_code` varchar(150) COLLATE utf8mb4_bin NOT NULL COMMENT '版本名称',
  `publish_desc` varchar(150) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '版本描述',
  `create_at` datetime NOT NULL COMMENT '创建时间',
  `create_by` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '创建人',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uniq_pc` (`publish_code`) USING BTREE,
  KEY `idx_pid` (`parent_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='flow发布日志';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mf_input_config`
--

DROP TABLE IF EXISTS `mf_input_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `mf_input_config` (
  `input_config_id` bigint(20) NOT NULL COMMENT '输出参数配置ID',
  `flow_config_id` bigint(20) NOT NULL COMMENT 'FLOW配置ID',
  `node_config_id` bigint(20) DEFAULT NULL COMMENT 'NODE配置ID',
  `scope` char(2) COLLATE utf8mb4_bin NOT NULL COMMENT '参数作用域',
  `service_key` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '服务参数名',
  `value_type` varchar(15) COLLATE utf8mb4_bin NOT NULL COMMENT '参数值类型',
  `optional` tinyint(1) NOT NULL COMMENT '是否可选',
  `assign_value_way` char(2) COLLATE utf8mb4_bin NOT NULL COMMENT '参数赋值类型',
  `context_key` varchar(80) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '上下文参数名',
  `adhoc_value` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '参数常量值',
  `creator` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modifier` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '修改人',
  `modified_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `remarks` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`input_config_id`) USING BTREE,
  UNIQUE KEY `uniq_f_n_s` (`flow_config_id`,`node_config_id`,`service_key`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='输入参数配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mf_node_config`
--

DROP TABLE IF EXISTS `mf_node_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `mf_node_config` (
  `node_config_id` bigint(20) NOT NULL COMMENT 'NODE配置ID',
  `flow_config_id` bigint(20) NOT NULL COMMENT 'FLOW配置ID',
  `name` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT 'NODE名',
  `service_type` varchar(30) COLLATE utf8mb4_bin NOT NULL COMMENT '服务类型',
  `function` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '服务功能',
  `biz_retry_count` int(11) NOT NULL COMMENT '业务重试次数',
  `biz_retry_interval` int(11) NOT NULL COMMENT '业务重试间隔',
  `timeout` int(11) NOT NULL COMMENT '技术超时时间',
  `graph_node_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '图中NODE的ID',
  `tag` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '标签',
  `color` varchar(30) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '颜色',
  `background` tinyint(1) NOT NULL DEFAULT '0' COMMENT '后台异步执行',
  `creator` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modifier` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '修改人',
  `modified_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`node_config_id`) USING BTREE,
  UNIQUE KEY `uniq_f_g` (`flow_config_id`,`graph_node_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='NODE配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mf_node_graph`
--

DROP TABLE IF EXISTS `mf_node_graph`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `mf_node_graph` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `flow_config_id` bigint(20) NOT NULL COMMENT 'FLOW配置ID',
  `node_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '图中NODE的ID',
  `node_name` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT 'NODE名',
  `node_type` char(2) COLLATE utf8mb4_bin NOT NULL COMMENT '节点类型',
  `x` int(11) NOT NULL COMMENT '位置横坐标',
  `y` int(11) NOT NULL COMMENT '位置纵坐标',
  `creator` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modifier` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '修改人',
  `modified_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `node_property` text COLLATE utf8mb4_bin COMMENT '赋值节点属性',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uniq_f_n` (`flow_config_id`,`node_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='NODE图形属性表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mf_output_config`
--

DROP TABLE IF EXISTS `mf_output_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `mf_output_config` (
  `output_config_id` bigint(20) NOT NULL COMMENT '输出参数配置ID',
  `flow_config_id` bigint(20) NOT NULL COMMENT 'FLOW配置ID',
  `node_config_id` bigint(20) DEFAULT NULL COMMENT 'NODE配置ID',
  `scope` char(2) COLLATE utf8mb4_bin NOT NULL COMMENT '参数作用域',
  `service_key` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '服务参数名',
  `value_type` varchar(15) COLLATE utf8mb4_bin NOT NULL COMMENT '参数值类型',
  `export_value_type` varchar(15) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '导出时参数值类型',
  `assign_value_way` char(2) COLLATE utf8mb4_bin DEFAULT 'CT',
  `context_key` varchar(80) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '上下文参数名',
  `fail_default_val` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '失败时的默认值',
  `stop_when_null` tinyint(1) NOT NULL COMMENT '参数为空时是否中止流程',
  `creator` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modifier` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '修改人',
  `modified_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `remarks` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`output_config_id`) USING BTREE,
  UNIQUE KEY `uniq_f_n_s` (`flow_config_id`,`node_config_id`,`service_key`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='输出参数配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mf_script`
--

DROP TABLE IF EXISTS `mf_script`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `mf_script` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `code` varchar(128) NOT NULL COMMENT '编码',
  `name` varchar(128) NOT NULL DEFAULT '' COMMENT '名称',
  `language` varchar(32) NOT NULL DEFAULT 'Groovy' COMMENT '语言',
  `description` text COMMENT '脚本描述',
  `max_version` int(11) NOT NULL DEFAULT '0' COMMENT '最大版本',
  `online_version` int(11) NOT NULL DEFAULT '0' COMMENT '在线版本',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态， 0 下线， 1 上线',
  `create_by` varchar(100) NOT NULL DEFAULT '' COMMENT '创建人',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(100) NOT NULL DEFAULT '' COMMENT '最后修改人',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_code` (`code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='计算脚本';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mf_script_version`
--

DROP TABLE IF EXISTS `mf_script_version`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `mf_script_version` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `code` varchar(128) NOT NULL COMMENT '编码',
  `version` int(11) NOT NULL DEFAULT '1' COMMENT '版本',
  `description` text COMMENT '描述',
  `content` text COMMENT '脚本内容',
  `can_update` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否可更新',
  `create_by` varchar(100) NOT NULL DEFAULT '' COMMENT '创建人',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(100) NOT NULL DEFAULT '' COMMENT '最后修改人',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_cv` (`code`,`version`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='脚本版本';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mf_transfer`
--

DROP TABLE IF EXISTS `mf_transfer`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `mf_transfer` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `flow_config_id` bigint(20) NOT NULL COMMENT 'FLOW配置ID',
  `from_node_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '起点 ID',
  `to_node_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '终点 ID',
  `expression` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '表达式',
  `creator` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modifier` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '修改人',
  `modified_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uniq_f_f_t` (`flow_config_id`,`from_node_id`,`to_node_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='(节点)连线表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `node_202209`
--

DROP TABLE IF EXISTS `node_202209`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `node_202209` (
  `node_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `node_config_id` bigint(20) NOT NULL COMMENT 'node配置模板id',
  `flow_id` bigint(20) NOT NULL COMMENT '运行时flowd',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `tag` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '业务标记',
  `status` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '状态',
  `service_type` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '服务类型',
  `function` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '功能',
  `fail_reason` varchar(300) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '失败原因',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `execute_count` int(11) NOT NULL DEFAULT '0' COMMENT '执行次数',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`node_id`) USING BTREE,
  KEY `idx_flow_id` (`flow_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='运行时节点';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `node_202210`
--

DROP TABLE IF EXISTS `node_202210`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `node_202210` (
  `node_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `node_config_id` bigint(20) NOT NULL COMMENT 'node配置模板id',
  `flow_id` bigint(20) NOT NULL COMMENT '运行时flowd',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `tag` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '业务标记',
  `status` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '状态',
  `service_type` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '服务类型',
  `function` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '功能',
  `fail_reason` varchar(300) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '失败原因',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `execute_count` int(11) NOT NULL DEFAULT '0' COMMENT '执行次数',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`node_id`) USING BTREE,
  KEY `idx_flow_id` (`flow_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='运行时节点';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `node_202503`
--

DROP TABLE IF EXISTS `node_202503`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `node_202503` (
  `node_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `node_config_id` bigint(20) NOT NULL COMMENT 'node配置模板id',
  `flow_id` bigint(20) NOT NULL COMMENT '运行时flowd',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `tag` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '业务标记',
  `status` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '状态',
  `service_type` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '服务类型',
  `function` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '功能',
  `fail_reason` varchar(300) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '失败原因',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `execute_count` int(11) NOT NULL DEFAULT '0' COMMENT '执行次数',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`node_id`) USING BTREE,
  KEY `idx_flow_id` (`flow_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='运行时节点';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `node_202504`
--

DROP TABLE IF EXISTS `node_202504`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `node_202504` (
  `node_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `node_config_id` bigint(20) NOT NULL COMMENT 'node配置模板id',
  `flow_id` bigint(20) NOT NULL COMMENT '运行时flowd',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `tag` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '业务标记',
  `status` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '状态',
  `service_type` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '服务类型',
  `function` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '功能',
  `fail_reason` varchar(300) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '失败原因',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `execute_count` int(11) NOT NULL DEFAULT '0' COMMENT '执行次数',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`node_id`) USING BTREE,
  KEY `idx_flow_id` (`flow_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='运行时节点';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `node_202505`
--

DROP TABLE IF EXISTS `node_202505`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `node_202505` (
  `node_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `node_config_id` bigint(20) NOT NULL COMMENT 'node配置模板id',
  `flow_id` bigint(20) NOT NULL COMMENT '运行时flowd',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `tag` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '业务标记',
  `status` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '状态',
  `service_type` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '服务类型',
  `function` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '功能',
  `fail_reason` varchar(300) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '失败原因',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `execute_count` int(11) NOT NULL DEFAULT '0' COMMENT '执行次数',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`node_id`) USING BTREE,
  KEY `idx_flow_id` (`flow_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='运行时节点';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `node_202506`
--

DROP TABLE IF EXISTS `node_202506`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `node_202506` (
  `node_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `node_config_id` bigint(20) NOT NULL COMMENT 'node配置模板id',
  `flow_id` bigint(20) NOT NULL COMMENT '运行时flowd',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `tag` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '业务标记',
  `status` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '状态',
  `service_type` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '服务类型',
  `function` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '功能',
  `fail_reason` varchar(300) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '失败原因',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `execute_count` int(11) NOT NULL DEFAULT '0' COMMENT '执行次数',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`node_id`) USING BTREE,
  KEY `idx_flow_id` (`flow_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='运行时节点';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `node_202507`
--

DROP TABLE IF EXISTS `node_202507`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `node_202507` (
  `node_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `node_config_id` bigint(20) NOT NULL COMMENT 'node配置模板id',
  `flow_id` bigint(20) NOT NULL COMMENT '运行时flowd',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `tag` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '业务标记',
  `status` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '状态',
  `service_type` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '服务类型',
  `function` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '功能',
  `fail_reason` varchar(300) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '失败原因',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `execute_count` int(11) NOT NULL DEFAULT '0' COMMENT '执行次数',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`node_id`) USING BTREE,
  KEY `idx_flow_id` (`flow_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='运行时节点';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `node_202508`
--

DROP TABLE IF EXISTS `node_202508`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `node_202508` (
  `node_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `node_config_id` bigint(20) NOT NULL COMMENT 'node配置模板id',
  `flow_id` bigint(20) NOT NULL COMMENT '运行时flowd',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `tag` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '业务标记',
  `status` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '状态',
  `service_type` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '服务类型',
  `function` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '功能',
  `fail_reason` varchar(300) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '失败原因',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `execute_count` int(11) NOT NULL DEFAULT '0' COMMENT '执行次数',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`node_id`) USING BTREE,
  KEY `idx_flow_id` (`flow_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='运行时节点';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `node_202509`
--

DROP TABLE IF EXISTS `node_202509`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `node_202509` (
  `node_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `node_config_id` bigint(20) NOT NULL COMMENT 'node配置模板id',
  `flow_id` bigint(20) NOT NULL COMMENT '运行时flowd',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `tag` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '业务标记',
  `status` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '状态',
  `service_type` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '服务类型',
  `function` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '功能',
  `fail_reason` varchar(300) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '失败原因',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `execute_count` int(11) NOT NULL DEFAULT '0' COMMENT '执行次数',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`node_id`) USING BTREE,
  KEY `idx_flow_id` (`flow_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='运行时节点';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `node_202510`
--

DROP TABLE IF EXISTS `node_202510`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `node_202510` (
  `node_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `node_config_id` bigint(20) NOT NULL COMMENT 'node配置模板id',
  `flow_id` bigint(20) NOT NULL COMMENT '运行时flowd',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `tag` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '业务标记',
  `status` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '状态',
  `service_type` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '服务类型',
  `function` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '功能',
  `fail_reason` varchar(300) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '失败原因',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `execute_count` int(11) NOT NULL DEFAULT '0' COMMENT '执行次数',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`node_id`) USING BTREE,
  KEY `idx_flow_id` (`flow_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='运行时节点';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `node_202511`
--

DROP TABLE IF EXISTS `node_202511`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `node_202511` (
  `node_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `node_config_id` bigint(20) NOT NULL COMMENT 'node配置模板id',
  `flow_id` bigint(20) NOT NULL COMMENT '运行时flowd',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `tag` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '业务标记',
  `status` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '状态',
  `service_type` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '服务类型',
  `function` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '功能',
  `fail_reason` varchar(300) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '失败原因',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `execute_count` int(11) NOT NULL DEFAULT '0' COMMENT '执行次数',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`node_id`) USING BTREE,
  KEY `idx_flow_id` (`flow_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='运行时节点';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `node_202512`
--

DROP TABLE IF EXISTS `node_202512`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `node_202512` (
  `node_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `node_config_id` bigint(20) NOT NULL COMMENT 'node配置模板id',
  `flow_id` bigint(20) NOT NULL COMMENT '运行时flowd',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `tag` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '业务标记',
  `status` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '状态',
  `service_type` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '服务类型',
  `function` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '功能',
  `fail_reason` varchar(300) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '失败原因',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `execute_count` int(11) NOT NULL DEFAULT '0' COMMENT '执行次数',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`node_id`) USING BTREE,
  KEY `idx_flow_id` (`flow_id`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='运行时节点';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `order_info`
--

DROP TABLE IF EXISTS `order_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `order_info` (
  `id` bigint(20) unsigned NOT NULL COMMENT '主键ID',
  `customer_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '客户id',
  `customer_code` varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '客户编码',
  `product_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '产品id',
  `product_code` varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '产品编码',
  `order_status` smallint(6) NOT NULL DEFAULT '2' COMMENT '状态',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uniq_cpcode` (`product_code`,`customer_code`) USING BTREE,
  KEY `idx_cid` (`customer_id`) USING BTREE,
  KEY `idx_cc` (`customer_code`) USING BTREE,
  KEY `idx_pid` (`product_id`) USING BTREE,
  KEY `idx_ct` (`create_at`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='订购信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `order_price_strategy`
--

DROP TABLE IF EXISTS `order_price_strategy`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `order_price_strategy` (
  `id` bigint(20) unsigned NOT NULL COMMENT '主键ID',
  `order_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '订购id',
  `customer_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '客户id',
  `customer_code` varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '客户编码',
  `product_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '产品id',
  `product_code` varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '产品编码',
  `charge_type` smallint(6) NOT NULL DEFAULT '1' COMMENT '计费方式',
  `charge_unit` smallint(6) NOT NULL DEFAULT '1' COMMENT '计费单元',
  `start` datetime NOT NULL COMMENT '开始时间',
  `end` datetime NOT NULL COMMENT '结束时间',
  `amount` decimal(20,6) NOT NULL DEFAULT '0.000000' COMMENT '价格',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_oid` (`order_id`) USING BTREE,
  KEY `idx_cc` (`customer_code`) USING BTREE,
  KEY `idx_pc` (`product_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='订购价格策略';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `product_flow_configs`
--

DROP TABLE IF EXISTS `product_flow_configs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product_flow_configs` (
  `product_config_id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `product_id` bigint(20) unsigned NOT NULL COMMENT '产品id',
  `product_code` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '产品编码',
  `task_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '业务阶段',
  `flow_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '依赖的外部flow名称',
  `enable` tinyint(4) DEFAULT NULL COMMENT '是否启用',
  `sync_flag` tinyint(4) DEFAULT NULL COMMENT '是否同步',
  `timeout_seconds` int(10) unsigned NOT NULL COMMENT '同步超时时间设置',
  `bind_at` datetime NOT NULL COMMENT '绑定时间',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`product_config_id`) USING BTREE,
  UNIQUE KEY `uniq_product_flow` (`product_code`,`flow_name`) USING BTREE,
  KEY `idx_productcode_tasktype` (`product_code`,`task_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='产品流程配置信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `product_flow_history`
--

DROP TABLE IF EXISTS `product_flow_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product_flow_history` (
  `id` int(10) unsigned NOT NULL COMMENT '主键',
  `product_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '产品编码',
  `flow_code` varchar(80) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '工作流名称',
  `sync_flag` tinyint(4) DEFAULT NULL COMMENT '是否同步',
  `timeout_seconds` int(10) unsigned NOT NULL COMMENT '同步超时时间设置',
  `bind_at` datetime NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT '绑定时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据创建时间-绑定时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_pcode` (`product_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='产品流程关联历史表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `product_flow_history_old`
--

DROP TABLE IF EXISTS `product_flow_history_old`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product_flow_history_old` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `product_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '产品编码',
  `flow_name` varchar(80) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '工作流名称',
  `sync_flag` tinyint(4) DEFAULT NULL COMMENT '是否同步',
  `timeout_seconds` int(10) unsigned NOT NULL COMMENT '同步超时时间设置',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='产品流程关联历史表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `product_info`
--

DROP TABLE IF EXISTS `product_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product_info` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '产品编码',
  `name` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '产品名称',
  `product_status` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'offline' COMMENT '状态',
  `is_customized` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否专属',
  `publish_time` datetime DEFAULT NULL COMMENT '发布时间',
  `description` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '产品介绍',
  `function_description` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '产品功能描述',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `product_level1` int(11) NOT NULL DEFAULT '-1' COMMENT '产品类型',
  `product_level2` int(11) NOT NULL DEFAULT '-1' COMMENT '产品类型',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uniq_code` (`code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='产品信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `rate_limit`
--

DROP TABLE IF EXISTS `rate_limit`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `rate_limit` (
  `id` int(11) NOT NULL COMMENT '主键ID',
  `customer` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '客户编码',
  `product` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '产品编码',
  `replenish_rate` int(11) NOT NULL COMMENT 'QPS',
  `burst_capacity` int(11) NOT NULL COMMENT '突发QPS',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uniq_workspace_product` (`customer`,`product`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='限流配置';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trace_logs_202209`
--

DROP TABLE IF EXISTS `trace_logs_202209`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `trace_logs_202209` (
  `trace_log_id` bigint(20) NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `node_name` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '节点名称',
  `node_group` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '节点组',
  `node_uuid` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '节点配置uuid',
  `status` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '状态',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`trace_log_id`) USING BTREE,
  KEY `idx_product` (`product`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='节点日志';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trace_logs_202210`
--

DROP TABLE IF EXISTS `trace_logs_202210`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `trace_logs_202210` (
  `trace_log_id` bigint(20) NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `node_name` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '节点名称',
  `node_group` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '节点组',
  `node_uuid` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '节点配置uuid',
  `status` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '状态',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`trace_log_id`) USING BTREE,
  KEY `idx_product` (`product`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='节点日志';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trace_logs_202503`
--

DROP TABLE IF EXISTS `trace_logs_202503`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `trace_logs_202503` (
  `trace_log_id` bigint(20) NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `node_name` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '节点名称',
  `node_group` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '节点组',
  `node_uuid` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '节点配置uuid',
  `status` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '状态',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`trace_log_id`) USING BTREE,
  KEY `idx_product` (`product`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='节点日志';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trace_logs_202504`
--

DROP TABLE IF EXISTS `trace_logs_202504`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `trace_logs_202504` (
  `trace_log_id` bigint(20) NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `node_name` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '节点名称',
  `node_group` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '节点组',
  `node_uuid` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '节点配置uuid',
  `status` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '状态',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`trace_log_id`) USING BTREE,
  KEY `idx_product` (`product`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='节点日志';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trace_logs_202505`
--

DROP TABLE IF EXISTS `trace_logs_202505`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `trace_logs_202505` (
  `trace_log_id` bigint(20) NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `node_name` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '节点名称',
  `node_group` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '节点组',
  `node_uuid` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '节点配置uuid',
  `status` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '状态',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`trace_log_id`) USING BTREE,
  KEY `idx_product` (`product`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='节点日志';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trace_logs_202506`
--

DROP TABLE IF EXISTS `trace_logs_202506`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `trace_logs_202506` (
  `trace_log_id` bigint(20) NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `node_name` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '节点名称',
  `node_group` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '节点组',
  `node_uuid` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '节点配置uuid',
  `status` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '状态',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`trace_log_id`) USING BTREE,
  KEY `idx_product` (`product`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='节点日志';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trace_logs_202507`
--

DROP TABLE IF EXISTS `trace_logs_202507`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `trace_logs_202507` (
  `trace_log_id` bigint(20) NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `node_name` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '节点名称',
  `node_group` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '节点组',
  `node_uuid` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '节点配置uuid',
  `status` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '状态',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`trace_log_id`) USING BTREE,
  KEY `idx_product` (`product`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='节点日志';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trace_logs_202508`
--

DROP TABLE IF EXISTS `trace_logs_202508`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `trace_logs_202508` (
  `trace_log_id` bigint(20) NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `node_name` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '节点名称',
  `node_group` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '节点组',
  `node_uuid` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '节点配置uuid',
  `status` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '状态',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`trace_log_id`) USING BTREE,
  KEY `idx_product` (`product`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='节点日志';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trace_logs_202509`
--

DROP TABLE IF EXISTS `trace_logs_202509`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `trace_logs_202509` (
  `trace_log_id` bigint(20) NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `node_name` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '节点名称',
  `node_group` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '节点组',
  `node_uuid` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '节点配置uuid',
  `status` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '状态',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`trace_log_id`) USING BTREE,
  KEY `idx_product` (`product`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='节点日志';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trace_logs_202510`
--

DROP TABLE IF EXISTS `trace_logs_202510`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `trace_logs_202510` (
  `trace_log_id` bigint(20) NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `node_name` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '节点名称',
  `node_group` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '节点组',
  `node_uuid` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '节点配置uuid',
  `status` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '状态',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`trace_log_id`) USING BTREE,
  KEY `idx_product` (`product`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='节点日志';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trace_logs_202511`
--

DROP TABLE IF EXISTS `trace_logs_202511`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `trace_logs_202511` (
  `trace_log_id` bigint(20) NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `node_name` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '节点名称',
  `node_group` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '节点组',
  `node_uuid` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '节点配置uuid',
  `status` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '状态',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`trace_log_id`) USING BTREE,
  KEY `idx_product` (`product`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='节点日志';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trace_logs_202512`
--

DROP TABLE IF EXISTS `trace_logs_202512`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `trace_logs_202512` (
  `trace_log_id` bigint(20) NOT NULL COMMENT '主键',
  `customer` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',
  `product` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '产品名',
  `apply_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '申请ID',
  `inner_apply_id` bigint(20) NOT NULL COMMENT '内部申请ID',
  `node_name` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '节点名称',
  `node_group` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '节点组',
  `node_uuid` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '节点配置uuid',
  `status` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '状态',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `apply_date` int(11) NOT NULL COMMENT '申请日期',
  PRIMARY KEY (`trace_log_id`) USING BTREE,
  KEY `idx_product` (`product`) USING BTREE,
  KEY `idx_inner_apply_id` (`inner_apply_id`) USING BTREE,
  KEY `idx_apply_id` (`apply_id`) USING BTREE,
  KEY `idx_workspace` (`customer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='节点日志';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `unique_record`
--

DROP TABLE IF EXISTS `unique_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `unique_record` (
  `id` bigint(20) unsigned NOT NULL COMMENT '主键ID',
  `biz_type` tinyint(3) NOT NULL COMMENT '要去重的业务',
  `unique_key` varchar(128) NOT NULL COMMENT '唯一键',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uniq_b_u` (`biz_type`,`unique_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='去重表';
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-21 23:18:40
