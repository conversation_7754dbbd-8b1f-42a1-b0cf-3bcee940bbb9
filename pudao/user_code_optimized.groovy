// 工具函数
def isValid = { value -> value != null && value != '' && value != -1 && value != '-1' }

// 测试数据先定义
def testContext = [
    EMP09S968: 10, EMP08S968: 5, EMP01S968: 15, EMP04S968: 8,
    EMP02S968: 20, EMP07S968: 3, EMP10S968: 12
]

// 版本1：在您的代码基础上的直接优化
def processV1(context) {
    // 映射关系定义
    def mapping = [
        'EMP09S968': 'oc101',
        'EMP08S968': 'oc102', 
        'EMP01S968': 'oc103',
        'EMP04S968': 'oc104',
        'EMP02S968': 'oc105',
        'EMP07S968': 'oc106',
        'EMP10S968': 'oc107'
    ]

    // 输入值数组（按照排名顺序）
    def inputValues = [
        [value: context?.EMP09S968, key: 'EMP09S968'],
        [value: context?.EMP08S968, key: 'EMP08S968'],
        [value: context?.EMP01S968, key: 'EMP01S968'], 
        [value: context?.EMP04S968, key: 'EMP04S968'],
        [value: context?.EMP02S968, key: 'EMP02S968'],
        [value: context?.EMP07S968, key: 'EMP07S968'],
        [value: context?.EMP10S968, key: 'EMP10S968']
    ]

    // 过滤有效值，找最大值，返回结果（一行搞定）
    def validValues = inputValues.findAll { isValid(it.value) }
    return validValues ? 
        [JOBPRO01: mapping[validValues.max { it.value }.key], flag: 1] : 
        [JOBPRO01: '-1', flag: 0]
}

println "版本1结果: ${processV1(testContext)}"

// 版本2：更简洁的函数式写法
def processV2(context) {
    def configs = [
        [emp: 'EMP09S968', oc: 'oc101'], [emp: 'EMP08S968', oc: 'oc102'],
        [emp: 'EMP01S968', oc: 'oc103'], [emp: 'EMP04S968', oc: 'oc104'],
        [emp: 'EMP02S968', oc: 'oc105'], [emp: 'EMP07S968', oc: 'oc106'],
        [emp: 'EMP10S968', oc: 'oc107']
    ]
    
    def winner = configs.findAll { isValid(context?."${it.emp}") }
                       .max { context."${it.emp}" }
    
    return winner ? [JOBPRO01: winner.oc, flag: 1] : [JOBPRO01: '-1', flag: 0]
}

// 版本3：保持您的结构，但用更简洁的语法
def processV3(context) {
    def mapping = [EMP09S968: 'oc101', EMP08S968: 'oc102', EMP01S968: 'oc103', 
                   EMP04S968: 'oc104', EMP02S968: 'oc105', EMP07S968: 'oc106', EMP10S968: 'oc107']

    def validValues = mapping.collect { key, oc -> [value: context?."$key", key: key, oc: oc] }
                            .findAll { isValid(it.value) }

    return validValues ? 
        [JOBPRO01: validValues.max { it.value }.oc, flag: 1] : 
        [JOBPRO01: '-1', flag: 0]
}

// 版本4：终极简化版本
def processV4(context) {
    return [EMP09S968: 'oc101', EMP08S968: 'oc102', EMP01S968: 'oc103', EMP04S968: 'oc104', 
            EMP02S968: 'oc105', EMP07S968: 'oc106', EMP10S968: 'oc107']
        .findAll { k, v -> isValid(context?."$k") }
        .max { k, v -> context."$k" }
        ?.with { k, v -> [JOBPRO01: v, flag: 1] } 
        ?: [JOBPRO01: '-1', flag: 0]
}

println "版本2结果: ${processV2(testContext)}"
println "版本3结果: ${processV3(testContext)}"
println "版本4结果: ${processV4(testContext)}"

// 测试边界情况
def emptyContext = [EMP09S968: null, EMP08S968: -1, EMP01S968: '', EMP04S968: -1, 
                   EMP02S968: null, EMP07S968: '', EMP10S968: -1]

println "\n边界情况测试:"
println "版本1结果: ${processV1(emptyContext)}"
println "版本2结果: ${processV2(emptyContext)}"
println "版本3结果: ${processV3(emptyContext)}"
println "版本4结果: ${processV4(emptyContext)}"

println "\n🎯 推荐："
println "• 如果喜欢您现在的结构: 使用版本3"
println "• 如果追求最简洁: 使用版本4"
println "• 如果要保持原样但稍微优化: 使用您当前代码+工具函数" 