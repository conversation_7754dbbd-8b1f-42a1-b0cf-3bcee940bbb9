#!/bin/bash

# 简易实时日志格式化工具，适合 docker service logs JSON 格式日志处理
# 适合配合 logstash-logback-encoder 输出的 JSON 格式日志

if [ $# -ne 1 ]; then
  echo "Usage: $0 <docker-service-name>"
  exit 1
fi

SERVICE=$1

# 实时拉取 docker service 日志并格式化输出
docker service logs --raw -f ${SERVICE} \
  | jq -Rr '
    fromjson? 
    | select(.) 
    | "\(.["@timestamp"]) [\(.level)] [\(.thread)] \(.logger):\(.caller_line) - \(.message) \((.stack_trace // "") | gsub("\\n"; " "))"'
