这里有10台机器作为生产环境，每台机器的配置如下：

# 以下三台部署高可用中间件 zookeeper rabbitmq redis
CPU:2核;内存:4G;系统盘:40G;数据盘:50G
1. 10.32.168.4	XG-prod-credit-mashanghui-01  
2. 10.32.168.5	XG-prod-credit-mashanghui-02
3. 10.32.168.6	XG-prod-credit-mashanghui-03

# 以下4台有两台部署主从MySQL，一台部署Clickhouse，一台部署日志收集框架如ELK或者其他轻量级日志收集框架
CPU:4核;内存:16G;系统盘:40G;数据盘:100G
4. 10.32.168.7	XG-prod-credit-mashanghui-04  
5. 10.32.168.8	XG-prod-credit-mashanghui-05
6. 10.32.168.9	XG-prod-credit-mashanghui-06
7. 10.32.168.10	XG-prod-credit-mashanghui-07

# 以下三台部署应用服务
CPU:4核;内存:32G;系统盘:40G;数据盘:50G
8. 10.32.168.11	XG-prod-credit-mashanghui-08
9. 10.32.168.14	XG-prod-credit-mashanghui-09
10. 10.32.168.15 XG-prod-credit-mashanghui-10
