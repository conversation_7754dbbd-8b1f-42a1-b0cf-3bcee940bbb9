## 测试环境部署

### MySQL 5.7.44

```sh

mkdir -p /data/mysql
sudo ln -s /data/mysql /var/lib/mysql


wget https://cdn.mysql.com/Downloads/MySQL-5.7/mysql-5.7.44-1.el7.x86_64.rpm-bundle.tar
tar -xvf mysql-5.7.44-1.el7.x86_64.rpm-bundle.tar
cd mysql-5.7.44-1.el7.x86_64.rpm-bundle
sudo rpm -ivh mysql-community-{common,libs,client,server}-*.rpm
sudo systemctl enable --now mysqld
systemctl status mysqld
sudo grep 'temporary password' /var/log/mysqld.log

# 修改root密码: he8dVFyLsga$893
sudo mysql_secure_installation 

```

### Zookeeper 3.8.4

```sh
rpm -ivh jdk-8u451-linux-x64.rpm

wget https://dlcdn.apache.org/zookeeper/zookeeper-3.8.4/apache-zookeeper-3.8.4-bin.tar.gz
sudo useradd -r -s /sbin/nologin zookeeper
sudo mkdir -p /opt/zookeeper/{data,logs}
sudo chown -R zookeeper:zookeeper /opt/zookeeper

tar -zxvf apache-zookeeper-3.8.4-bin.tar.gz
sudo mv apache-zookeeper-3.8.4-bin /opt/zookeeper/zookeeper-3.8.4
sudo chown -R zookeeper:zookeeper /opt/zookeeper/zookeeper-3.8.4


tee /opt/zookeeper/zookeeper-3.8.4/conf/zoo.cfg <<'EOF' > /dev/null
tickTime=2000
initLimit=10
syncLimit=5
dataDir=/opt/zookeeper/data
dataLogDir=/opt/zookeeper/logs
clientPort=2181
admin.enableServer=false

EOF

# 集群配置
echo 1 | sudo tee /opt/zookeeper/data/myid

sudo tee /etc/systemd/system/zookeeper.service <<'EOF' > /dev/null
[Unit]
Description=Apache ZooKeeper Server
Documentation=https://zookeeper.apache.org
After=network.target

[Service]
Type=simple
User=zookeeper
Group=zookeeper
ExecStart=/opt/zookeeper/zookeeper-3.8.4/bin/zkServer.sh start-foreground
ExecStop=/opt/zookeeper/zookeeper-3.8.4/bin/zkServer.sh stop
Restart=on-abnormal
Environment=ZOO_LOG_DIR=/opt/zookeeper/logs
Environment=ZOO_LOG4J_PROP="INFO,CONSOLE"

[Install]
WantedBy=multi-user.target

EOF

sudo systemctl daemon-reexec
sudo systemctl daemon-reload
sudo systemctl enable --now zookeeper
sudo systemctl status zookeeper

/opt/zookeeper/zookeeper-3.8.4/bin/zkCli.sh -server 127.0.0.1:2181
```



### RabbitMQ 3.11.28

```sh
wget https://github.com/rabbitmq/erlang-rpm/releases/download/v*********/erlang-*********-1.el7.x86_64.rpm
wget https://github.com/rabbitmq/rabbitmq-server/releases/download/v3.11.2/rabbitmq-server-3.11.2-1.el8.noarch.rpm

sudo rpm -ivh erlang*.rpm rabbitmq-server*.rpm
sudo systemctl enable --now rabbitmq-server
systemctl status rabbitmq-server

# 
sudo rabbitmqctl list_permissions -p /
sudo rabbitmqctl list_users
sudo rabbitmqctl list_user_permissions guest
sudo rabbitmqctl add_vhost punch-decision
rabbitmqctl set_permissions -p punch-decision guest ".*" ".*" ".*"

# 启用管理插件
sudo rabbitmq-plugins enable rabbitmq_management
wget http://localhost:15672/cli/rabbitmqadmin
chmod +x rabbitmqadmin
./rabbitmqadmin list queues

# 创建可远程登录的管理员账号
rabbitmqctl add_user admin admin
rabbitmqctl set_user_tags admin administrator
rabbitmqctl set_permissions -p / admin ".*" ".*" ".*"

```



### Redis 7.0.5

```sh
wget https://rpmfind.net/linux/remi/enterprise/7/remi/x86_64/redis-7.2.4-1.el7.remi.x86_64.rpm
sudo rpm -ivh redis-7.2.4-1.el7.remi.x86_64.rpm

sudo systemctl enable --now redis
sudo systemctl status redis
redis-cli ping
```

### Clickhouse

```sh

mkdir -p /data/clickhouse
sudo ln -s /data/clickhouse /var/lib/clickhouse

wget https://packages.clickhouse.com/rpm/stable/clickhouse-server-*********.x86_64.rpm
wget https://packages.clickhouse.com/rpm/stable/clickhouse-client-*********.x86_64.rpm
wget https://packages.clickhouse.com/rpm/stable/clickhouse-common-static-*********.x86_64.rpm

rpm -ivh *

# 临时修改内存页不合并
cat /sys/kernel/mm/transparent_hugepage/enabled
echo never | sudo tee /sys/kernel/mm/transparent_hugepage/enabled
# 永久修改内存页不合并
sed -i 's/GRUB_CMDLINE_LINUX="/GRUB_CMDLINE_LINUX="transparent_hugepage=never /' /etc/default/grub && sudo grub2-mkconfig -o /boot/grub2/grub.cfg


systemctl start clickhouse-server
systemctl status clickhouse-server

CREATE DATABASE IF NOT EXISTS punch_decision_argus;

tee /etc/clickhouse-server/users.d/admin.xml <<'EOF' > /dev/null
<clickhouse>
    <users>
        <admin>
            <password_sha256_hex>b270fb5106916ea1363a501600359bdd05ca03415e24d381130be2e52dbb94b8</password_sha256_hex>
            <profile>default</profile>
            <quota>default</quota>
            <networks>
                <ip>::/0</ip>
            </networks>
        </admin>
    </users>

    <profiles>
        <default>
            <max_memory_usage>10000000000</max_memory_usage>
        </default>
    </profiles>

    <quotas>
        <default>
            <interval>
                <duration>3600</duration>
                <queries>0</queries>
            </interval>
        </default>
    </quotas>
</clickhouse>
EOF
systemctl restart clickhouse-server


# 数据目录迁移 本地 -aP 远程 -avzh
# sudo rsync -aP /var/lib/clickhouse/ /data/clickhouse/

# cp /etc/clickhouse-server/config.xml /etc/clickhouse-server/config.xml.bak
# sudo sed -i 's#/var/lib/clickhouse#/data/clickhouse#g' /etc/clickhouse-server/config.xml

```

### Elastic-Job

```sh

/etc/systemd/system/elastic-job-console.service


tee /etc/systemd/system/elastic-job-console.service <<'EOF' > /dev/null
[Unit]
Description=Elastic Job Console Service
After=network.target

[Service]
Type=simple
ExecStart=/opt/elastic-job/elastic-job-console/bin/start.sh -p 8080
WorkingDirectory=/opt/elastic-job/elastic-job-console
Restart=on-failure
RestartSec=5s

[Install]
WantedBy=multi-user.target
EOF
systemctl daemon-reload
systemctl enable --now elastic-job-console
systemctl start elastic-job-console
systemctl restart elastic-job-console
systemctl status elastic-job-console


```


### DNS

```sh
yum install dnsmasq -y
vim /etc/dnsmasq.conf

sudo tee -a /etc/dnsmasq.conf <<'EOF' > /dev/null

# 监听接口（127.0.0.1 和本机内网 IP）
listen-address=127.0.0.1,***********
# 域名后缀
domain=unirap

# 禁用默认 DNS 解析行为
no-resolv
server=**********
addn-hosts=/etc/hosts

# DNS 缓存大小（加速内网解析）
cache-size=1000
EOF

systemctl enable --now dnsmasq
systemctl status dnsmasq

# 配置本机DNS服务
sudo tee /etc/resolv.conf <<'EOF' > /dev/null
nameserver 127.0.0.1
EOF
# 禁止修改
chattr +i /etc/resolv.conf
# 允许修改
chattr -i /etc/resolv.conf


sudo tee /etc/hosts <<'EOF' > /dev/null
*********** zeus.qa.unirap
EOF

# dig 能找到解析
dig @127.0.0.1 zeus.unirap
dig zeus.unirap


```

### YApi

```sh
tee /etc/yum.repos.d/mongodb-org-6.0.repo <<'EOF' > /dev/null
[mongodb-org-6.0]
name=MongoDB Repository
baseurl=https://repo.mongodb.org/yum/redhat/$releasever/mongodb-org/6.0/x86_64/
gpgcheck=1
enabled=1
gpgkey=https://www.mongodb.org/static/pgp/server-6.0.asc
EOF

[mongodb-org]
name=MongoDB Repository
baseurl=https://mirrors.tuna.tsinghua.edu.cn/mongodb/yum/el7-6.0/
gpgcheck=0
enabled=1


cd mongo-rpms
yum install --downloadonly --downloaddir=./ mongodb-org
tar -zcvf mongo-rpms.tgz mongo-rpms

rpm -ivh *

sudo mkdir -p /data/mongo/data
sudo mkdir -p /data/mongo/log
sudo chown -R mongod:mongod /data/mongo

sudo tee /etc/mongod.conf <<'EOF' > /dev/null
systemLog:
  destination: file
  logAppend: true
  # 【修改】将日志路径指向我们新创建的目录
  path: /data/mongo/log/mongod.log

# Where and how to store data.
storage:
  # 【修改】将数据路径指向我们新创建的目录
  dbPath: /data/mongo/data
  journal:
    enabled: true

# processManagement forks the process and runs in the background.
processManagement:
  fork: true  # run in background
  pidFilePath: /var/run/mongodb/mongod.pid  # location of pidfile
  timeZoneInfo: /usr/share/zoneinfo

# network interfaces
net:
  port: 27017
  # 【关键修改】允许远程连接
  # 默认是 127.0.0.1 (只允许本机连接)
  # 修改为 0.0.0.0 来监听所有网络接口
  bindIp: 0.0.0.0


# 【安全配置】我们先不启用它，等创建完用户后再回来开启
security:
  authorization: enabled
EOF

systemctl enable --now mongod
systemctl status mongod

# 无密码连接
mongosh
# 1. 切换到 admin 数据库，这是存放用户凭证的地方
use admin

# 2. 创建用户，用户名和密码与 YApi 的 config.json 完全对应
db.createUser(
  {
    user: "root",
    pwd: "Root.123.", // 请确保这个密码与 YApi 配置中的一致
    roles: [ { role: "root", db: "admin" } ]
  }
)

systemctl restart mongod

# -u 用户名, -p 会提示输入密码, --authenticationDatabase 指定去哪个库里验证身份
mongosh --port 27017 --authenticationDatabase "admin" -u "root" -p





```

```yaml
services:

  yapi:
    image: docker-registry.local:5000/yapi:1.0.0
    volumes:
      - /data/yapi/config.json:/app/config.json
    deploy:
      mode: replicated
      replicas: 1
      placement:
        constraints:
          - node.labels.ops == true
      restart_policy:
        condition: on-failure
    networks:
      - shared-net
    ports:
      - "3300:3000"

  yapi-mongo:
    image: docker-registry.local:5000/mongo:6.0.24
    volumes:
      - /data/yapi/mongo:/data/db
    environment:
      MONGO_INITDB_ROOT_USERNAME: root
      MONGO_INITDB_ROOT_PASSWORD: Root.123.
    deploy:
      mode: replicated
      replicas: 1
      placement:
        constraints:
          - node.labels.ops == true
      restart_policy:
        condition: on-failure
    networks:
      - shared-net

```

### nodejs12

```sh
curl -sL https://deb.nodesource.com/setup_12.x | sudo -E bash -
mkdir -p /data/tmp/nodejs12-rpms
yum install --downloadonly --downloaddir=/data/tmp/nodejs12-rpms nodejs
```