// 最优雅版本 - 基于您的代码改进
def processMapping(context) {
    // 映射关系定义 (可以考虑从配置文件读取)
    def mapping = [
        EMP09S968: 'oc101', EMP08S968: 'oc102', EMP01S968: 'oc103', EMP04S968: 'oc104',
        EMP02S968: 'oc105', EMP07S968: 'oc106', EMP10S968: 'oc107'
    ]
    
    // 一行实现：过滤有效值 -> 找最大值 -> 返回结果
    def winner = mapping.findAll { key, _ -> isValid(context[key]) }
                       .max { key, _ -> context[key] }
    
    return winner ? [JOBPRO01: winner.value, flag: 1] : [JOBPRO01: '-1', flag: 0]
}

// 工具方法：判断值是否有效
def isValid(value) {
    value != null && value != '' && value != -1 && value != '-1'
}

// 备用版本1：如果需要保持排名优先级
def processMappingWithPriority(context) {
    def configs = [
        [key: 'EMP09S968', oc: 'oc101'], [key: 'EMP08S968', oc: 'oc102'], 
        [key: 'EMP01S968', oc: 'oc103'], [key: 'EMP04S968', oc: 'oc104'],
        [key: 'EMP02S968', oc: 'oc105'], [key: 'EMP07S968', oc: 'oc106'], 
        [key: 'EMP10S968', oc: 'oc107']
    ]
    
    def winner = configs.findAll { isValid(context[it.key]) }
                       .max { context[it.key] }
    
    return winner ? [JOBPRO01: winner.oc, flag: 1] : [JOBPRO01: '-1', flag: 0]
}

// 备用版本2：函数式风格（如果您喜欢链式调用）
def processMappingFunctional(context) {
    ['EMP09S968', 'EMP08S968', 'EMP01S968', 'EMP04S968', 'EMP02S968', 'EMP07S968', 'EMP10S968']
        .zip(['oc101', 'oc102', 'oc103', 'oc104', 'oc105', 'oc106', 'oc107'])
        .findAll { emp, oc -> isValid(context[emp]) }
        .max { emp, oc -> context[emp] }
        ?.with { emp, oc -> [JOBPRO01: oc, flag: 1] } 
        ?: [JOBPRO01: '-1', flag: 0]
}

// 终极简化版本（如果项目允许使用更多Groovy特性）
def processMappingUltimate(context) {
    [EMP09S968: 'oc101', EMP08S968: 'oc102', EMP01S968: 'oc103', EMP04S968: 'oc104', EMP02S968: 'oc105', EMP07S968: 'oc106', EMP10S968: 'oc107']
        .findAll { k, v -> isValid(context[k]) }
        .max { k, v -> context[k] }
        ?.with { k, v -> [JOBPRO01: v, flag: 1] } 
        ?: [JOBPRO01: '-1', flag: 0]
}

// 您当前代码的直接优化版本（保持结构不变，只是简化）
def processMappingOptimized(context) {
    def mapping = [
        EMP09S968: 'oc101', EMP08S968: 'oc102', EMP01S968: 'oc103', EMP04S968: 'oc104',
        EMP02S968: 'oc105', EMP07S968: 'oc106', EMP10S968: 'oc107'
    ]

    def validValues = mapping.findAll { key, oc -> isValid(context[key]) }
                             .collect { key, oc -> [value: context[key], key: key, oc: oc] }

    if (!validValues) {
        return [JOBPRO01: '-1', flag: 0]
    }

    def maxItem = validValues.max { it.value }
    return [JOBPRO01: maxItem.oc, flag: 1]
}

// 测试代码
def testContext1 = [
    EMP09S968: 10, EMP08S968: 5, EMP01S968: 15, EMP04S968: 8,
    EMP02S968: 20, EMP07S968: 3, EMP10S968: 12
]

def testContext2 = [
    EMP09S968: null, EMP08S968: -1, EMP01S968: '', 
    EMP04S968: -1, EMP02S968: null, EMP07S968: '', EMP10S968: -1
]

def testContext3 = [
    EMP09S968: 20, EMP08S968: 15, EMP01S968: 20, EMP04S968: 10,
    EMP02S968: 18, EMP07S968: 20, EMP10S968: 12
]

println "=== 正常情况测试 ==="
println "主版本: ${processMapping(testContext1)}"
println "优先级版本: ${processMappingWithPriority(testContext1)}"
println "函数式版本: ${processMappingFunctional(testContext1)}"
println "终极简化版本: ${processMappingUltimate(testContext1)}"
println "优化版本: ${processMappingOptimized(testContext1)}"

println "\n=== 边界情况测试 ==="
println "主版本: ${processMapping(testContext2)}"
println "优先级版本: ${processMappingWithPriority(testContext2)}"
println "函数式版本: ${processMappingFunctional(testContext2)}"
println "终极简化版本: ${processMappingUltimate(testContext2)}"
println "优化版本: ${processMappingOptimized(testContext2)}"

println "\n=== 重复最大值测试 ==="
println "主版本: ${processMapping(testContext3)}"
println "优先级版本: ${processMappingWithPriority(testContext3)}"
println "函数式版本: ${processMappingFunctional(testContext3)}"
println "终极简化版本: ${processMappingUltimate(testContext3)}"
println "优化版本: ${processMappingOptimized(testContext3)}" 