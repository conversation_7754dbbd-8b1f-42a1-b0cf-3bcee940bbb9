# The MySQL server
[mysqld]
port                    = 3306
user                    = mysql
datadir                 = /data/mysql/data
tmpdir                  = /data/mysql/tmp
slave-load-tmpdir       = /tmp
socket                  = /data/mysql/socket/mysqld.sock
pid-file 		= /data/mysql/socket/mysqld.pid
key_buffer_size         = 64M
max_allowed_packet      = 64M
table_open_cache        = 2048
join_buffer_size        = 16M
sort_buffer_size        = 32M
read_buffer_size        = 16M
read_rnd_buffer_size    = 32M
myisam_sort_buffer_size = 128M
query_cache_size        = 0
query_cache_limit       = 2M
max_tmp_tables          = 256
tmp_table_size          = 128M
max_heap_table_size     = 128M
thread_cache_size       = 64
max_connections         = 8192
max_user_connections    = 8192 
max_connect_errors      = 99999999
long_query_time         = 1
slow-query-log          = 1
slow-query-log-file     = /data/mysql/log/mysql-slow.log
back_log                = 1024
myisam_repair_threads   db= 1
expire_logs_days        = 10
interactive_timeout	= 28800
wait_timeout		= 28800
transaction_isolation   = REPEATABLE-READ 
#explicit_defaults_for_timestamp = 1
server-id                    = 10235
report_host                    = **********
log-bin                 = /data/mysql/log/mysql-bin
relay-log               = /data/mysql/log/relay-bin
log-error               = /data/mysql/log/error.log
sql_mode                = 'STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION'	

read_only               = 1
log-slave-updates       = 1
skip-slave-start        = 1
skip-name-resolve       = 1 
character-set-server    = utf8mb4
lower_case_table_names  = 1
binlog_format		= ROW 
sync_binlog             = 1

gtid_mode 	 	  = ON
enforce-gtid-consistency  = TRUE
master-info-repository    = TABLE
relay-log-info-repository = TABLE
relay_log_recovery 	  = 1
binlog-checksum		  = CRC32 
master_verify_checksum    = 1
slave_sql_verify_checksum = 1
binlog-rows-query-log-events = 1

#------------- mysql 5.7 -------------
#loose_innodb_numa_interleave = 1
innodb_buffer_pool_dump_pct = 40
innodb_page_cleaners      = 10
innodb_undo_log_truncate  = 1
innodb_max_undo_log_size  = 2G
innodb_purge_rseg_truncate_frequency = 128
# new replication settings #
slave-parallel-type = DATABASE
slave-parallel-workers = 0
slave_preserve_commit_order= 1
slave_transaction_retries = 128
#other change settings #
binlog_gtid_simple_recovery= 1
log_timestamps            = system
show_compatibility_56     = ON
default_password_lifetime = 0

#plugin_load = "server_audit=server_audit.so;validate_password.so;rpl_semi_sync_master=semisync_master.so;rpl_semi_sync_slave=semisync_slave.so"
#---------- mariadb audit plugin -------
plugin_load = server_audit=server_audit.so
server_audit_logging      = 1
server_audit_events       = connect,query
server_audit_file_rotate_size = 20G
server_audit_file_rotations = 0
server_audit_excl_users   = ganglia
server_audit_output_type  = file
server_audit_file_path = /data/mysql/audit_log/server_audit.log

#--------- semi sync replication settings ---------
# plugin_load = "validate_password.so;rpl_semi_sync_master=semisync_master.so;rpl_semi_sync_slave=semisync_slave.so"
# rpl_semi_sync_master_enabled = 1
# rpl_semi_sync_master_timeout = 3000
# rpl_semi_sync_slave_enabled = 1

#-------------    innodb  --------------

default-storage-engine          = InnoDB
innodb_buffer_pool_size         = 30G
innodb_data_file_path           = ibdata1:100M:autoextend
innodb_flush_log_at_trx_commit  = 1
innodb_log_buffer_size          = 16M
innodb_log_file_size            = 1300M
innodb_log_files_in_group       = 3
innodb_max_dirty_pages_pct      = 50
innodb_lock_wait_timeout        = 60
innodb_file_per_table           = 1
innodb_flush_method 		= O_DIRECT
innodb_support_xa 		= 1
innodb_io_capacity 		= 20000
innodb_io_capacity_max          = 20000
innodb_buffer_pool_instances    = 10
innodb_write_io_threads 	= 16
innodb_read_io_threads 		= 16
innodb_strict_mode              = 1
innodb_rollback_on_timeout	= 1
innodb_open_files		= 4096
innodb_print_all_deadlocks      = 1
innodb_thread_concurrency	= 0
innodb_purge_threads            = 4
innodb_autoinc_lock_mode        = 1
innodb_flush_neighbors          = 0
innodb_sort_buffer_size         = 64M
innodb_lru_scan_depth           = 4096
innodb_undo_logs                = 128
innodb_undo_tablespaces         = 0
innodb_online_alter_log_max_size = 1G
innodb_buffer_pool_load_at_startup = 1
innodb_buffer_pool_dump_at_shutdown = 1

[mysqldump]
max_allowed_packet              = 512M

[myisamchk]
key_buffer                      = 64M
sort_buffer_size                = 32M
read_buffer                     = 16M
write_buffer                    = 16M