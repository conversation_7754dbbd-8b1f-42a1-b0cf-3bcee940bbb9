// 简单优雅的版本 - 基于您的代码改进

// 版本1：保持您的逻辑结构，简化语法
def yourOriginalLogicOptimized = {
    // 映射关系定义
    def mapping = [
        'EMP09S968': 'oc101', 'EMP08S968': 'oc102', 'EMP01S968': 'oc103',
        'EMP04S968': 'oc104', 'EMP02S968': 'oc105', 'EMP07S968': 'oc106', 'EMP10S968': 'oc107'
    ]

    // 输入值数组（按照排名顺序）
    def inputValues = [
        [value: context?.EMP09S968, key: 'EMP09S968'],
        [value: context?.EMP08S968, key: 'EMP08S968'],
        [value: context?.EMP01S968, key: 'EMP01S968'], 
        [value: context?.EMP04S968, key: 'EMP04S968'],
        [value: context?.EMP02S968, key: 'EMP02S968'],
        [value: context?.EMP07S968, key: 'EMP07S968'],
        [value: context?.EMP10S968, key: 'EMP10S968']
    ]

    // 过滤掉空值和-1的值
    def validValues = inputValues.findAll { item ->
        item.value != null && item.value != '' && item.value != -1 && item.value != '-1'
    }

    // 如果没有有效值，返回默认结果
    if (validValues.isEmpty()) {
        return [JOBPRO01: '-1', flag: 0]
    }

    // 找到最大值，取排名靠前的
    def maxItem = validValues.max { it.value }

    // 获取对应的oc字段
    def ocField = mapping[maxItem.key]

    return [JOBPRO01: ocField, flag: 1]
}

// 版本2：超简洁版本
def superSimple = {
    [EMP09S968: 'oc101', EMP08S968: 'oc102', EMP01S968: 'oc103', EMP04S968: 'oc104', 
     EMP02S968: 'oc105', EMP07S968: 'oc106', EMP10S968: 'oc107']
        .findAll { k, v -> context?."$k" != null && context?."$k" != '' && context?."$k" != -1 && context?."$k" != '-1' }
        .max { k, v -> context?."$k" }
        ?.with { k, v -> [JOBPRO01: v, flag: 1] } ?: [JOBPRO01: '-1', flag: 0]
}

// 版本3：中等复杂度，易于理解
def balanced = {
    def configs = [
        [emp: 'EMP09S968', oc: 'oc101'], [emp: 'EMP08S968', oc: 'oc102'],
        [emp: 'EMP01S968', oc: 'oc103'], [emp: 'EMP04S968', oc: 'oc104'],
        [emp: 'EMP02S968', oc: 'oc105'], [emp: 'EMP07S968', oc: 'oc106'],
        [emp: 'EMP10S968', oc: 'oc107']
    ]
    
    def valid = configs.findAll { cfg ->
        def value = context?."${cfg.emp}"
        value != null && value != '' && value != -1 && value != '-1'
    }
    
    if (!valid) return [JOBPRO01: '-1', flag: 0]
    
    def winner = valid.max { context?."${it.emp}" }
    return [JOBPRO01: winner.oc, flag: 1]
}

// 测试
def testCases = [
    normal: [EMP09S968: 10, EMP08S968: 5, EMP01S968: 15, EMP04S968: 8, EMP02S968: 20, EMP07S968: 3, EMP10S968: 12],
    empty: [EMP09S968: null, EMP08S968: -1, EMP01S968: '', EMP04S968: -1, EMP02S968: null, EMP07S968: '', EMP10S968: -1],
    duplicate: [EMP09S968: 20, EMP08S968: 15, EMP01S968: 20, EMP04S968: 10, EMP02S968: 18, EMP07S968: 20, EMP10S968: 12]
]

testCases.each { name, data ->
    context = data
    println "${name.toUpperCase()}情况:"
    println "  版本1(保持您的逻辑): ${yourOriginalLogicOptimized()}"
    println "  版本2(超简洁):     ${superSimple()}"
    println "  版本3(平衡版):     ${balanced()}"
    println ""
}

println "🎯 根据您的代码，最推荐的优雅改进："
println """
// 在您现有代码基础上的最小改动：
def validValues = inputValues.findAll { item ->
    item.value != null && item.value != '' && item.value != -1 && item.value != '-1'
}

return validValues.isEmpty() ? 
    [JOBPRO01: '-1', flag: 0] : 
    [JOBPRO01: mapping[validValues.max { it.value }.key], flag: 1]
""" 