#!/bin/bash

# --- 配置部分 ---
TARGET_MYSQL_HOST="localhost" # 修改为你的目标 MySQL 主机名或 IP 地址
TARGET_MYSQL_USER="root"
# DDL 文件所在的目录
DDL_FILES_DIR="./ddl_files"

# --- 脚本执行部分 ---

# 检查 DDL 文件目录是否存在
if [ ! -d "$DDL_FILES_DIR" ]; then
    echo "错误: DDL 文件目录 '$DDL_FILES_DIR' 不存在。"
    exit 1
fi

# 安全地获取目标 MySQL 实例的密码
echo -n "请输入目标 MySQL 用户 '$TARGET_MYSQL_USER'@'$TARGET_MYSQL_HOST' 的密码: "
read -s TARGET_MYSQL_PASSWORD # -s 选项使得输入不回显
echo # 在密码输入后换行

echo ""
echo "开始批量导入 DDL 文件从目录: $DDL_FILES_DIR 到目标实例: $TARGET_MYSQL_HOST"
echo "--------------------------------------------------"

SUCCESS_COUNT=0
FAIL_COUNT=0

# 遍历目录中所有的 .sql 文件
for DDL_FILE in "$DDL_FILES_DIR"/*.sql; do
    if [ -f "$DDL_FILE" ]; then # 检查是否是文件
        echo "正在导入文件: $DDL_FILE ..."

        # 执行 mysql 导入命令
        mysql -h "$TARGET_MYSQL_HOST" -u "$TARGET_MYSQL_USER" -p"$TARGET_MYSQL_PASSWORD" < "$DDL_FILE"

        if [ $? -eq 0 ]; then
            echo "成功导入: $DDL_FILE"
            SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
        else
            echo "错误: 导入 $DDL_FILE 失败。请检查错误信息。"
            FAIL_COUNT=$((FAIL_COUNT + 1))
            # 你可以选择在这里让脚本失败时退出，或者继续尝试导入其他文件
            # exit 1 # 如果希望任何一个文件导入失败则立即停止，取消此行注释
        fi
        echo "--------------------------------------------------"
    fi
done

echo ""
echo "批量导入任务完成。"
echo "成功导入 $SUCCESS_COUNT 个 DDL 文件。"
if [ $FAIL_COUNT -gt 0 ]; then
    echo "失败 $FAIL_COUNT 个 DDL 文件。"
fi

unset TARGET_MYSQL_PASSWORD