#!/bin/bash

set -e

REGISTRY_URL="http://docker-registry.local:5000"
KEEP_TAGS=3
REGISTRY_DATA_DIR="/data/docker-registry/data"
REGISTRY_CONFIG_DIR="/data/docker-registry/config"

# 遍历所有镜像仓库
REPOS=$(curl -s ${REGISTRY_URL}/v2/_catalog | jq -r '.repositories[]')

for repo in ${REPOS}; do
  echo ">>> 仓库: ${repo}"

  TAGS=$(curl -s ${REGISTRY_URL}/v2/${repo}/tags/list | jq -r '.tags[]' | sort -V)
  TAG_COUNT=$(echo "$TAGS" | wc -l)

  if [[ "$TAG_COUNT" -le "$KEEP_TAGS" ]]; then
    echo "  - 总共 ${TAG_COUNT} 个 tag，小于等于保留数，跳过"
    continue
  fi

  # 计算需要删除的 tag 列表
  DELETE_TAGS=$(echo "$TAGS" | head -n -${KEEP_TAGS})

  for tag in ${DELETE_TAGS}; do
    echo "  - 删除 tag: ${tag}"

    # 获取对应 manifest digest
    DIGEST=$(curl -s -H "Accept: application/vnd.docker.distribution.manifest.v2+json" \
      ${REGISTRY_URL}/v2/${repo}/manifests/${tag} \
      -I | grep Docker-Content-Digest | awk '{print $2}' | tr -d $'\r')

    if [[ -z "$DIGEST" ]]; then
      echo "  - 无法获取 digest，跳过"
      continue
    fi

    # 删除 manifest
    curl -s -X DELETE ${REGISTRY_URL}/v2/${repo}/manifests/${DIGEST}
    echo "  - 删除完成: ${DIGEST}"
  done
done

echo ">>> 所有旧 tag 清理完成，准备垃圾回收..."

# 停止 registry 服务
docker compose -f /data/docker-registry/docker-compose.yml down

# 执行垃圾回收
docker run --rm -v ${REGISTRY_DATA_DIR}:/var/lib/registry \
  -v ${REGISTRY_CONFIG_DIR}:/etc/docker/registry \
  registry:2.8.3 garbage-collect /etc/docker/registry/config.yml

# 启动 registry 服务
docker compose -f /data/docker-registry/docker-compose.yml up
