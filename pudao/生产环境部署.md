
## 生产环境部署

### HOST

```sh
************    docker-registry.local
***********     middleware-01
***********     middleware-02
***********     middleware-03

```

### 准备

```sh
sed -i.bak '/^# add by baseos/,$d' /etc/profile
rm -rf /export/*
ln -s /export /data
```

### Docker安装

************
************
************

```sh
yum install -y yum-utils
yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo

# 境内
# yum-config-manager --add-repo=https://mirrors.cloud.tencent.com/docker-ce/linux/centos/docker-ce.repo
# sed -i "s/download.docker.com/mirrors.tencentyun.com\/docker-ce/g"  /etc/yum.repos.d/docker-ce.repo

yum install --downloadonly --downloaddir=./docker-rpms docker-ce
tar -zcvf docker-rpms.tgz docker-rpms
tar -zxvf docker-rpms.tgz


# docker数据目录软连接到数据分区
# mkdir -p /data/docker
# ln -s /data/docker /var/lib/docker


cd docker-rpms
yum localinstall -y *.rpm


systemctl enable --now docker
systemctl status docker

# docker数据目录软连接到数据分区
# systemctl stop docker
# systemctl status docker
# mkdir -p /data/docker/
# rsync -aP /var/lib/docker/ /data/docker/
# mv /var/lib/docker /var/lib/docker.bak
# ln -s /data/docker /var/lib/docker
# systemctl start docker
# systemctl status docker

# rm -rf /var/lib/docker.bak
```

### Docker Swarm

```sh
docker swarm init --advertise-addr ************

# 加入Worker
# docker swarm join --token SWMTKN-1-xxxxxxxx... ************:2377

# 生成工作节点加入集群的token
# docker swarm join-token worker
# 生成管理节点加入集群的token
# docker swarm join-token manager

# 加入manager
docker swarm join-token manager

docker swarm join --token SWMTKN-1-zzzzzzzzzzzzzzzzzz-zzzzzzzzzzzzzzzzzz ************:2377

# 验证
docker node ls

# 解散集群
docker swarm leave --force
```

### 镜像仓库

************

```sh
docker pull registry:2.8.3
docker save -o registry.tar registry:2.8.3
docker load -i registry.tar

mkdir -p /data/docker-registry/{config,data}


tee /data/docker-registry/config/config.yml <<'EOF' > /dev/null
version: 0.1
log:
  fields:
    service: registry
storage:
  filesystem:
    rootdirectory: /var/lib/registry
  delete:
    enabled: true
http:
  addr: :5000
EOF

tee /data/docker-registry/docker-compose.yml <<'EOF' > /dev/null
services:
  registry:
    image: registry:2.8.3
    container_name: registry
    restart: always
    ports:
      - "5000:5000"
    volumes:
      - /data/docker-registry/data:/var/lib/registry
      - /data/docker-registry/config:/etc/docker/registry
EOF

docker compose up -d

curl http://127.0.0.1:5000/v2/_catalog


# 配置信任镜像仓库
tee /etc/docker/daemon.json <<'EOF' > /dev/null
{
  "insecure-registries" : ["docker-registry.local:5000"]
}
EOF
systemctl restart docker
systemctl status docker

```

### 卸载MySQL

```sh
# 卸载自带mariadb
rpm -e --nodeps mariadb-libs

sudo systemctl stop mysqld
sudo systemctl disable mysqld
rpm -qa | grep -i mysql-community
sudo yum remove -y mysql-community-server mysql-community-client mysql-community-libs mysql-community-common

rm -rf /data/mysql/*
rm -rf /var/lib/mysql
rm -rf /var/log/mysqld.log
```

### MySQL 5.7.44

MHA Node 1 (Master): *********** (mysql-master)
MHA Node 2 (Slave): *********** (mysql-slave)


```sh

# mkdir -p /data/mysql
# ln -s /data/mysql /var/lib/mysql


wget https://cdn.mysql.com/Downloads/MySQL-5.7/mysql-5.7.44-1.el7.x86_64.rpm-bundle.tar
tar -xvf mysql-5.7.44-1.el7.x86_64.rpm-bundle.tar
cd mysql-5.7.44-1.el7.x86_64.rpm-bundle
# rpm -ivh mysql-community-{common,libs,client,server}-*.rpm
yum localinstall * -y


# 配置文件 
mkdir -p /data/mysql/{data,run,log,tmp}
chown -R mysql:mysql /data/mysql
vim /etc/my.cnf

# 初始化
mysqld --user=mysql --initialize

systemctl enable --now mysqld
systemctl status mysqld
grep 'temporary password' /data/mysql/log/error.log

# 修改root密码: BL0MykmuSd2Yv6i3$^
mysql_secure_installation

```

### MySQL 主从复制

```sh
# master slave 都新增复制用户
grant replication slave on *.* to 'repl'@'%' identified by 'Repl.123';
flush privileges;

# master当前状态 master_log_file='mysql-bin.000003',master_log_pos=589
show master status;

# 查看binlog
show binlog events in 'mysql-bin.000002' from 589;
$ mysqlbinlog --base64-output=decode-rows -vv --start-position=589 mysql-bin.000003

show slave status \G
# 从库执行
change master to master_host='***********',master_port=3306,master_user='repl',master_password='Repl.123',master_auto_position=1;
show slave status \G

# 开始同步
start slave;

show slave status \G
# Slave_IO_Running: Yes
# Slave_SQL_Running: Yes


SHOW VARIABLES WHERE Variable_name IN ('relay_log_purge', 'read_only');


# 从库设置只读，但是拥有super权限的账号不受限制
set global read_only=1;
# 从库关闭中继日志的清除，MHA在发生切换的过程中，从库的恢复过程中依赖于relay log的相关信息，所以这里要将relay log的自动清除设置为OFF，采用手动清除relay log的方式。
# set global relay_log_purge=0;
```

### MHA

MHA Manager (Primary) ************
MHA Manager (Standby) ***********


- 保证MHA节点能免密SSH登录MySQL节点

```sh
yum provides ssh-keygen
yum install openssh -y
# 所有节点ssh互联互通，所有公钥 汇总到 ~/.ssh/authorized_keys 然后再分发到所有节点
ssh-keygen -t rsa -b 4096 -C 'MHA'
# 汇总公钥
cat ~/.ssh/id_rsa.pub
# 到
vim ~/.ssh/authorized_keys
# 不提示自动添加到 ~/.ssh/known_hosts
ssh -o StrictHostKeyChecking=no -q ***********

```

- 安装

```sh
# 准备离线安装包
mkdir mha-rpms
sudo yum install --downloadonly --downloaddir=./mha-rpms mha4mysql-manager mha4mysql-node perl-DBD-MySQL perl-Config-Tiny perl-Log-Dispatch perl-Parallel-ForkManager
cd mha-rpms
wget https://github.com/yoshinorim/mha4mysql-manager/releases/download/v0.58/mha4mysql-manager-0.58-0.el7.centos.noarch.rpm
wget https://github.com/yoshinorim/mha4mysql-node/releases/download/v0.58/mha4mysql-node-0.58-0.el7.centos.noarch.rpm

tar -zcvf mha-rpms.tgz mha-rpms

# mysql 节点
tar -zxvf mha-rpms.tgz
cd mha-rpms
rm -rf mha*manager*
yum localinstall -y *.rpm

# MHA Manager 节点
yum localinstall -y *.rpm
```

- 配置

```sh

# MHA默认是从指定路径读取mysql和mysqlbinlog执行文件的,确保MySQL节点上存在
# ln -s $(which mysqlbinlog) /usr/bin/mysqlbinlog
# ln -s $(which mysql) /usr/bin/mysql

grant all privileges on *.* to 'mha'@"%" identified by "Mha..123";
flush privileges; 

mkdir -p /var/log/mha/mysql
touch /var/log/mha/mysql/mysql.log

mkdir -p /etc/mha
vim /etc/mha/mysql.cnf


# 检查SSH连通性
masterha_check_ssh --conf=/etc/mha/mysql.cnf
# 检查主从状态
masterha_check_repl --conf=/etc/mha/mysql.cnf

# 检查监控状态
masterha_check_status --conf=/etc/mha/mysql.cnf

# 开始监控
nohup masterha_manager --conf=/etc/mha/mysql.cnf --ignore_last_failover > /var/log/mha/mysql.log 2>&1 &
# 停止监控
masterha_stop --conf=/etc/mha/mysql.cnf
# 手动切换
masterha_master_switch --conf=/etc/mha/mysql.cnf --master_state=dead --dead_master_host=*********** --dead_master_port=3306 --new_master_host=*********** --new_master_port=3306 --ignore_last_failover
# 手动切换
masterha_master_switch --conf=/etc/mha/mysql.cnf --master_state=alive

SELECT @@global.read_only, @@global.super_read_only;
show slave hosts;
show slave status\G
```

- 配置文件

```ini
[server default]
# MySQL 连接用户，MHA 用它检查复制状态
user=mha
password=Mha..123

# MySQL 复制时使用的用户（MHA 在重指向时需要）
repl_user=repl
repl_password=Repl.123

# MHA Manager 通过 SSH 连接时使用的系统用户
ssh_user=root
ssh_port=22
ping_interval=2

# --- MHA Manager 自身配置 ---
manager_workdir=/var/lib/mha/mysql
manager_log=/var/log/mha/mysql.log

master_ip_failover_script=/etc/mha/mha_vip_failover.sh
master_ip_online_change_script=/etc/mha/mha_vip_failover.sh

# --- 数据库节点配置 ---
[server1]
hostname=***********
port=3306
master_binlog_dir=/data/mysql/data

[server2]
hostname=***********
port=3306
master_binlog_dir=/data/mysql/data

# 关键配置：将从库标记为候选主库，故障时优先提升它
candidate_master=1
```

- master_ip_failover_script

/etc/mha/mha_vip_failover.sh

```sh
#!/usr/bin/env bash
# ------------------------------------------------------------
# mha_vip_failover.sh
# 适用：master_ip_failover_script / master_ip_online_change_script
# 功能：在 MHA 执行主从切换时，仅在旧主机上重启 keepalived，
#       触发 VIP 漂移；start 阶段不做额外动作。
# 
# 日期：2025‑06‑16
# ------------------------------------------------------------

set -euo pipefail

############### 可按需调整 ################
LOG_FILE="/var/log/mha/mha_vip_failover.log"
SSH_OPTS="-o StrictHostKeyChecking=no -o ConnectTimeout=5"
KEEPALIVED_SERVICE="keepalived"
##########################################

mkdir -p "$(dirname "$LOG_FILE")"
exec >>"$LOG_FILE" 2>&1

log() { echo "[$(date '+%F %T')] $*"; }

# ---------- 预声明变量，防 unbound ----------
COMMAND=""
ORIG_MASTER_HOST="" ORIG_MASTER_IP="" ORIG_MASTER_PORT=""
NEW_MASTER_HOST=""  NEW_MASTER_IP=""  NEW_MASTER_PORT=""

# ---------- 解析 --key=value ----------
for ARG in "$@"; do
  case "$ARG" in
    --command=*)            COMMAND="${ARG#*=}"             ;;
    --orig_master_host=*)   ORIG_MASTER_HOST="${ARG#*=}"    ;;
    --orig_master_ip=*)     ORIG_MASTER_IP="${ARG#*=}"      ;;
    --orig_master_port=*)   ORIG_MASTER_PORT="${ARG#*=}"    ;;
    --new_master_host=*)    NEW_MASTER_HOST="${ARG#*=}"     ;;
    --new_master_ip=*)      NEW_MASTER_IP="${ARG#*=}"       ;;
    --new_master_port=*)    NEW_MASTER_PORT="${ARG#*=}"     ;;
    *) ;;  # 其他参数可在此继续添加
  esac
done

log "========== Script invoked =========="
log "RAW ARGS: $*"
log "COMMAND=${COMMAND:-}"
log "ORIG_MASTER_HOST=${ORIG_MASTER_HOST:-}  NEW_MASTER_HOST=${NEW_MASTER_HOST:-}"

######################## status ########################
if [[ "$COMMAND" == "status" ]]; then
  log "Health‑check OK (command=status)"
  exit 0
fi

################### stop / stopssh #####################
if [[ "$COMMAND" == "stop" || "$COMMAND" == "stopssh" ]]; then
  if [[ -n "$ORIG_MASTER_HOST" ]]; then
    log "Restarting $KEEPALIVED_SERVICE on OLD master: $ORIG_MASTER_HOST"
    if ssh $SSH_OPTS "$ORIG_MASTER_HOST" "sudo systemctl restart $KEEPALIVED_SERVICE"; then
      log "keepalived restarted successfully on $ORIG_MASTER_HOST"
      exit 0          # 成功
    else
      log "WARN: failed to restart keepalived on $ORIG_MASTER_HOST"
      exit 10         # 非致命，MHA 继续后续流程
    fi
  else
    log "ERROR: ORIG_MASTER_HOST missing"
    exit 1
  fi
fi

######################## start ########################
if [[ "$COMMAND" == "start" ]]; then
  log "Start phase: nothing to do (VIP 已由 VRRP 自动漂移)"
  exit 0
fi

######################## 兜底 ########################
log "ERROR: unknown or empty COMMAND '$COMMAND'"
exit 1

```

# 测试

```sh
chmod +x /etc/mha/master_ip_failover.sh
```


### Clickhouse

```sh

mkdir -p /data/clickhouse
sudo ln -s /data/clickhouse /var/lib/clickhouse

wget https://packages.clickhouse.com/rpm/stable/clickhouse-server-*********.x86_64.rpm
wget https://packages.clickhouse.com/rpm/stable/clickhouse-client-*********.x86_64.rpm
wget https://packages.clickhouse.com/rpm/stable/clickhouse-common-static-*********.x86_64.rpm

rpm -ivh *

# 临时修改内存页不合并
cat /sys/kernel/mm/transparent_hugepage/enabled
echo never | sudo tee /sys/kernel/mm/transparent_hugepage/enabled
# 永久修改内存页不合并
sed -i 's/GRUB_CMDLINE_LINUX="/GRUB_CMDLINE_LINUX="transparent_hugepage=never /' /etc/default/grub && sudo grub2-mkconfig -o /boot/grub2/grub.cfg


systemctl start clickhouse-server
systemctl status clickhouse-server

clickhouse-client
# 密码: Clickhouse.123
clickhouse-client --user default --password
# 密码: jO@Cj3nR%RDPiN92na
clickhouse-client --user admin --password


clickhouse-client --host *********** --user default --password
clickhouse-client --host *********** --user admin --password

CREATE DATABASE IF NOT EXISTS punch_decision_argus;

tee /etc/clickhouse-server/users.d/admin.xml <<'EOF' > /dev/null
<clickhouse>
    <users>
        <admin>
            <password_sha256_hex>383452f6c6bfaaa5a734396cc400a3120588ce747dd2ba7df1a44091a888a168</password_sha256_hex>
            <profile>default</profile>
            <quota>default</quota>
            <networks>
                <ip>::/0</ip>
            </networks>
        </admin>
    </users>

    <profiles>
        <default>
            <max_memory_usage>10000000000</max_memory_usage>
        </default>
    </profiles>

    <quotas>
        <default>
            <interval>
                <duration>3600</duration>
                <queries>0</queries>
            </interval>
        </default>
    </quotas>
</clickhouse>
EOF

# 不可变
# vim /etc/clickhouse-server/config.xml
vim /etc/clickhouse-server/config.d/config.xml
# 修改网络监听, 内存限制
<clickhouse>
    <listen_host>0.0.0.0</listen_host>
    <max_server_memory_usage>12000000000</max_server_memory_usage>
</clickhouse>

systemctl restart clickhouse-server


# 数据目录迁移 本地 -aP 远程 -avzh
# sudo rsync -aP /var/lib/clickhouse/ /data/clickhouse/

# cp /etc/clickhouse-server/config.xml /etc/clickhouse-server/config.xml.bak
# sudo sed -i 's#/var/lib/clickhouse#/data/clickhouse#g' /etc/clickhouse-server/config.xml

```



### Zookeeper 3.8.4

```sh
rpm -ivh jdk-8u451-linux-x64.rpm

wget https://dlcdn.apache.org/zookeeper/zookeeper-3.8.4/apache-zookeeper-3.8.4-bin.tar.gz
sudo useradd -r -s /sbin/nologin zookeeper
sudo mkdir -p /data/zookeeper/{app,data,logs}
sudo chown -R zookeeper:zookeeper /data/zookeeper

tar -zxvf apache-zookeeper-3.8.4-bin.tar.gz
sudo mv apache-zookeeper-3.8.4-bin /data/zookeeper/app/zookeeper-3.8.4
sudo chown -R zookeeper:zookeeper /data/zookeeper/app/zookeeper-3.8.4


tee /data/zookeeper/app/zookeeper-3.8.4/conf/zoo.cfg <<'EOF' > /dev/null
# 心跳基本时间单元，毫秒
tickTime=2000
# Leader 和 Follower 初始连接时能容忍的最多心跳数（10*2=20秒）
initLimit=10
# Leader 和 Follower 之间请求和应答时间的最大心跳数（5*2=10秒）
syncLimit=5
# 数据目录，必须指向我们创建的数据盘路径
dataDir=/data/zookeeper/data
# 客户端连接端口
clientPort=2181
# 单个客户端的最大连接数
maxClientCnxns=60
# 为了安全，禁用非必须的管理服务器
admin.enableServer=false

4lw.commands.whitelist=*

# 集群节点配置 (核心！)
# 格式: server.ID=HOSTNAME:PEER_PORT:LEADER_PORT
# PEER_PORT 用于节点间通信，LEADER_PORT 用于 Leader 选举
server.1=***********:2888:3888
server.2=***********:2888:3888
server.3=***********:2888:3888

EOF

# 集群配置
echo 1 | sudo tee /data/zookeeper/data/myid
echo 2 | sudo tee /data/zookeeper/data/myid
echo 3 | sudo tee /data/zookeeper/data/myid

sudo tee /etc/systemd/system/zookeeper.service <<'EOF' > /dev/null
[Unit]
Description=Apache ZooKeeper Server
Documentation=https://zookeeper.apache.org
After=network.target

[Service]
Type=simple
User=zookeeper
Group=zookeeper
ExecStart=/data/zookeeper/app/zookeeper-3.8.4/bin/zkServer.sh start-foreground
ExecStop=/data/zookeeper/app/zookeeper-3.8.4/bin/zkServer.sh stop
Restart=on-abnormal
Environment=ZOO_LOG_DIR=/data/zookeeper/logs
Environment=ZOO_LOG4J_PROP="INFO,CONSOLE"

[Install]
WantedBy=multi-user.target

EOF

sudo systemctl daemon-reload
sudo systemctl enable --now zookeeper
sudo systemctl status zookeeper
sudo systemctl restart zookeeper

# ruok - Are you okay?
echo "ruok" | nc localhost 2181
# 如果服务正常，会返回 "imok"

# stat - 查看详细状态
echo "stat" | nc localhost 2181
# 会返回详细的统计信息，包括它的角色（Mode: leader/follower）

/data/zookeeper/app/zookeeper-3.8.4/bin/zkCli.sh -server 127.0.0.1:2181

/data/zookeeper/app/zookeeper-3.8.4/bin/zkServer.sh status
```


### RabbitMQ 3.11.28

```sh
wget https://github.com/rabbitmq/erlang-rpm/releases/download/v*********/erlang-*********-1.el7.x86_64.rpm
wget https://github.com/rabbitmq/rabbitmq-server/releases/download/v3.11.2/rabbitmq-server-3.11.2-1.el8.noarch.rpm

sudo rpm -ivh erlang*.rpm rabbitmq-server*.rpm

# 同步 Erlang Cookie ***********生成cookie
systemctl start rabbitmq-server
systemctl status rabbitmq-server
cat /var/lib/rabbitmq/.erlang.cookie

# 停止服务并清空节点状态:
sudo systemctl stop rabbitmq-server
sudo rabbitmqctl stop_app
sudo rabbitmqctl reset
sudo rabbitmqctl start_app
sudo systemctl start rabbitmq-server

# 同步cookie到另外两台机器
vim /var/lib/rabbitmq/.erlang.cookie
sudo chown rabbitmq:rabbitmq /var/lib/rabbitmq/.erlang.cookie
sudo chmod 400 /var/lib/rabbitmq/.erlang.cookie

# 三台启动独立的服务，共享了cookie的暗号
systemctl start rabbitmq-server

mkdir -p /data/rabbitmq/{data,logs}
chown -R rabbitmq.rabbitmq /data/rabbitmq

# 很重要，必须配置域名不能用IP，配置NODENAME
sudo tee /etc/rabbitmq/rabbitmq-env.conf <<'EOF' > /dev/null
RABBITMQ_MNESIA_BASE=/data/rabbitmq/data
RABBITMQ_LOG_BASE=/data/rabbitmq/logs
NODENAME=rabbit@middleware-01
EOF

sudo systemctl enable --now rabbitmq-server
systemctl status rabbitmq-server

systemctl enable rabbitmq-server


# 配置高可用策略 (镜像队列)
# 集群建立后，默认情况下队列并不会自动在所有节点上同步。我们需要通过“策略 (Policy)”来定义哪些队列需要被镜像。
# 在任意一个节点上执行即可，策略会自动同步到整个集群。
# 创建一个名为 "ha-all" 的策略
# 它会应用到所有 (.*) 队列
# 定义为 "ha-mode":"all"，意味着队列会被镜像到集群中的所有节点
sudo rabbitmqctl set_policy ha-all ".*" '{"ha-mode":"all"}' --priority 0 --apply-to queues


# 常见运维命令
sudo rabbitmqctl list_permissions -p /
sudo rabbitmqctl list_users
sudo rabbitmqctl list_user_permissions guest
sudo rabbitmqctl list_user_permissions admin
sudo rabbitmqctl add_vhost punch-decision
sudo rabbitmqctl list_vhosts
rabbitmqctl set_permissions -p punch-decision guest ".*" ".*" ".*"
rabbitmqctl set_permissions -p punch-decision admin ".*" ".*" ".*"

# 启用管理插件
sudo rabbitmq-plugins enable rabbitmq_management
wget http://localhost:15672/cli/rabbitmqadmin
chmod +x rabbitmqadmin
./rabbitmqadmin list queues

# 任意一个节点上执行即可
# 创建可远程登录的管理员账号
rabbitmqctl add_user admin admin
rabbitmqctl set_user_tags admin administrator
rabbitmqctl set_permissions -p / admin ".*" ".*" ".*"
rabbitmqctl list_users

systemctl restart rabbitmq-server

# 将.5加入。4
# 1. 停止 rabbitmq 应用（不是停止整个服务）
sudo rabbitmqctl stop_app
sudo rabbitmqctl reset
# 2. 加入到 .4 节点
sudo rabbitmqctl join_cluster rabbit@middleware-01
# 3. 重新启动应用
sudo rabbitmqctl start_app
```

### Redis

*********** (middleware-01): Redis Master    + Sentinel 1
*********** (middleware-02): Redis Replica 1 + Sentinel 2
*********** (middleware-03): Redis Replica 2 + Sentinel 3

```sh
wget https://rpmfind.net/linux/remi/enterprise/7/remi/x86_64/redis-7.2.4-1.el7.remi.x86_64.rpm
sudo rpm -ivh redis-7.2.4-1.el7.remi.x86_64.rpm

mkdir -p /data/redis
chown -R redis:redis /data/redis

mkdir -p /etc/redis
/bin/mv /etc/redis*.conf /etc/redis/

sudo tee /etc/redis/redis.conf <<'EOF' > /dev/null
# 绑定到所有网络接口，允许远程连接
bind 0.0.0.0

# 关闭保护模式，因为我们已经设置了密码
protected-mode no

# 设置端口
port 6379

# 以守护进程方式运行
daemonize yes

# PID 文件路径
pidfile /var/run/redis_6379.pid

# 日志文件路径
logfile /var/log/redis/redis_6379.log

# 数据目录
dir /data/redis

# 设置一个健壮的密码，主从库密码必须一致
requirepass Redis.123
masterauth Redis.123

replicaof middleware-01 6379
EOF

# 哨兵模式

sudo tee /etc/redis/sentinel.conf <<'EOF' > /dev/null
# 哨兵进程不以守护进程方式运行，由 systemd 管理
daemonize no

# 哨兵的 PID 文件
pidfile /var/run/redis-sentinel.pid

# 哨兵的日志文件
logfile /var/log/redis/sentinel.log

# 哨兵的工作目录
dir /tmp

# 核心配置：监控名为 "mymaster" 的主库
# 地址是 middleware-01:6379
# 最后的 2 表示，当有至少 2 个哨兵认为主库宕机时，才进行故障切换
sentinel monitor mymaster *********** 6379 2

# 主库连接密码
sentinel auth-pass mymaster Redis.123

# 主库被判定为下线的时间（毫秒）
sentinel down-after-milliseconds mymaster 30000

# 故障切换的超时时间（毫秒）
sentinel failover-timeout mymaster 180000

# 在同一时间，只能有一个从库在同步新主库的数据，这个值设为 1 可以加快切换速度
sentinel parallel-syncs mymaster 1
EOF

sudo systemctl enable --now redis
sudo systemctl status redis
redis-cli 
> auth Redis.123
> ping
> info replication

sudo systemctl enable --now redis-sentinel
sudo systemctl status redis-sentinel
redis-cli -p 26379
    # 查看哨兵监控的主库状态
    SENTINEL master mymaster
    # 您应该能看到主库的IP、端口、状态，以及从库和哨兵的数量

    # 查看从库的状态
    SENTINEL slaves mymaster

    # 查看其他哨兵的状态
    SENTINEL sentinels mymaster


```

### 日志PLG

```sh

docker pull grafana/promtail:3.5.1
docker pull grafana/loki:3.5.1
docker pull grafana/grafana:12.0.1-security-01

mkdir log-images
cd log-images

docker save -o promtail.tar grafana/promtail:3.5.1
docker save -o loki.tar grafana/loki:3.5.1
docker save -o grafana.tar grafana/grafana:12.0.1-security-01

tar -zcvf log-images.tgz log-images


tar -zxvf log-images.tgz
cd log-images
docker load -i loki.tar
docker load -i grafana.tar
# Promtail的镜像也加载进来，以备不时之需
docker load -i promtail.tar


mkdir -p /data/loki/{config,data}
mkdir -p /data/loki/data/{chunks,rules,compactor}
chown -R 10001:10001 /data/loki/data
chmod -R 755 /data/loki/data

tee /data/loki/loki-config.yaml <<'EOF' > /dev/null
# Loki v3.x 版本的简化配置文件
auth_enabled: false

server:
  http_listen_port: 3100
  grpc_listen_port: 9096

# `common` 配置块是 v3 的一个重要变化，用于统一配置
common:
  instance_addr: 127.0.0.1
  path_prefix: /loki  # Loki 存储数据的主目录
  storage:
    filesystem:
      chunks_directory: /loki/chunks
      rules_directory: /loki/rules
  replication_factor: 1
  ring:
    kvstore:
      store: inmemory

# schema_config 依然重要，但现在推荐使用 tsdb 索引
schema_config:
  configs:
    - from: 2025-06-16 # 使用一个近期的日期
      store: tsdb
      object_store: filesystem
      schema: v13
      index:
        prefix: index_
        period: 24h

# Ruler 和 aompactor 对于基础的日志收集不是必需的
ruler:
  alertmanager_url: http://localhost:9093

compactor:
  working_directory: /loki/compactor
  delete_request_store: filesystem
  compaction_interval: 10m
  retention_enabled: true
  retention_delete_delay: 2h
  retention_delete_worker_count: 150
EOF

tee /data/loki/docker-compose.yaml <<'EOF' > /dev/null
services:
  loki:
    image: grafana/loki:3.5.1
    container_name: loki
    ports:
      - "3100:3100"
    volumes:
      - /data/loki/loki-config.yaml:/etc/loki/local-config.yaml
      - /data/loki/data:/loki
    command: -config.file=/etc/loki/local-config.yaml
    restart: unless-stopped
EOF

docker compose up -d

mkdir -p /data/grafana/data
chown -R 472:472 /data/grafana/data
chmod -R 755 /data/grafana/data

tee /data/grafana/grafana.ini <<'EOF' > /dev/null

EOF
tee /data/grafana/grafana.ini <<'EOF' > /dev/null

EOF

tee /data/grafana/docker-compose.yaml <<'EOF' > /dev/null
services:
  grafana:
    image: grafana/grafana:12.0.1-security-01
    container_name: grafana
    ports:
      - "3000:3000"
    volumes:
      - /data/grafana/data:/var/lib/grafana
    restart: unless-stopped
EOF

docker compose up -d
docker compose down
docker ps
docker logs -f grafana
```

### Promtail

```sh
mkdir -p /var/lib/promtail
chown promtail:promtail /var/lib/promtail

# systemd 配置文件User改为root 或者
sudo usermod -aG docker promtail

tee /etc/promtail/config.yml <<'EOF' > /dev/null
server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /var/lib/promtail/positions.yaml

clients:
  - url: http://************:3100/loki/api/v1/push

scrape_configs:
  - job_name: docker-json-app
    static_configs:
      - labels:
          job: docker-logs
          host: ************
          __path__: /var/lib/docker/containers/*/*-json.log
    pipeline_stages:
      - docker: {}
      - json:
          expressions:
            app: "app"
            timestamp: "timestamp"
            level: "level"
            thread: "thread"
            logger: "logger"
            caller_line: "caller_line"
            message: "message"
            stack_trace: "stack_trace"
    relabel_configs:
      - source_labels: ['__path__']
        regex: "/var/lib/docker/containers/([a-f0-9]+)/\\\\1-json.log"
        target_label: 'container_id'
EOF
```

```yaml

# =========================================================
# Promtail 配置文件 (生产级优化版)
# =========================================================

# server 配置块: 定义 Promtail 自身的 HTTP 服务
server:
  http_listen_port: 9080
  grpc_listen_port: 0

# positions 配置块: 记录 Promtail 读取每个日志文件的位置
positions:
  filename: /var/lib/promtail/positions.yaml

# clients 配置块: 定义将日志发送到哪个 Loki 服务器
# 这是 Promtail 的核心配置之一，必须在顶层
clients:
  - url: http://************:3100/loki/api/v1/push

# scrape_configs 配置块: 定义要抓取哪些日志
scrape_configs:
  # - job_name: 定义一组抓取任务
  - job_name: docker-logs
    # 使用 Docker 服务发现，自动找到所有容器
    docker_sd_configs:
      - host: unix:///var/run/docker.sock
        refresh_interval: 5s
    
    # relabel_configs: 从 Docker 提供的元数据中处理和创建标签
    relabel_configs:
      # 为所有日志添加静态的 host 和 env 标签
      - source_labels: ['__address__']
        target_label: 'host'
        replacement: '${HOSTNAME}' # 使用环境变量获取主机名
      - target_label: 'env'
        replacement: 'prod' # 硬编码环境为 prod
        
      # 从 Docker 容器名中提取 `container` 标签
      - source_labels: ['__meta_docker_container_name']
        regex: '/(.*)'
        target_label: 'container'

      - source_labels: ['container']
        regex: 'app_([^\.]+)\..*'
        target_label: 'app'


    # pipeline_stages: 定义日志内容的处理流水线
    pipeline_stages:
      # 1. 剥离 Docker 的 JSON 日志外壳
      - docker: {}
      
      # 2. 解析 Spring Boot 输出的 JSON 日志内容
      - json:
          expressions:
            level: level
            logger: logger
      
      # 3. 将解析出的字段提升为 Loki 的索引标签
      - labels:
          level:
          logger:

```

### ElasticJob


```sh

tee -a /data/stack/docker-stack.yml <<'EOF' > /dev/null
services:
  elastic-job:
    image: docker-registry.local:5000/elastic-job-lite-console:latest
    environment:
      - ELASTIC_ROOT_USERNAME=user
      - ELASTIC_ROOT_PASSWORD=pass
    ports:
      - 8899:8899
EOF

docker stack deploy -c docker-stack.yml app --detach=false
```

### Prometheus

```sh

docker pull prom/prometheus:v3.4.1
docker save -o prometheus.tar prom/prometheus:v3.4.1
docker load -i prometheus.tar

mkdir -p /data/prometheus/{data,rules,config}
mkdir -p /data/prometheus/config/target

chown -R 65534:65534 /data/prometheus
chmod -R 755 /data/prometheus


tee /data/prometheus/docker-compose.yaml <<'EOF' > /dev/null
services:
    prometheus:
      image: prom/prometheus:v3.4.1
      container_name: prometheus
      ports:
        - "9090:9090"
      volumes:
        - /data/prometheus/config/prometheus.yml:/etc/prometheus/prometheus.yml
        - /data/prometheus/config/targets:/etc/prometheus/targets
        - /data/prometheus/data:/prometheus
      command:
        - '--config.file=/etc/prometheus/prometheus.yml'
        - '--storage.tsdb.path=/prometheus'
        - '--storage.tsdb.retention.time=15d'
        - '--web.enable-lifecycle'
      restart: unless-stopped
EOF

sudo -i
> /usr/local/node_exporter/config.yaml
systemctl restart node_exporter
curl http://localhost:9100/metrics
```

- /data/prometheus/config/prometheus.yml

> swarm配置在 /data/prometheus/config/targets/swarm_targets.json

```yml
global:
  scrape_interval: 30s
  evaluation_interval: 30s

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'node_exporter'
    static_configs:
      - targets:
          - '***********:9100'
          - '***********:9100'
          - '***********:9100'
          - '***********:9100'
          - '***********:9100'
          - '***********:9100'
          - '************:9100'
          - '************:9100'
          - '************:9100'
          - '************:9100'

  - job_name: 'swarm-services'
    file_sd_configs:
      - files:
        - '/etc/prometheus/targets/swarm_targets.json'

```


### Loki Grafana Prometheus

考虑到Prometheus自动发现Swarm集群内部容器的麻烦，且这三个服务可接受临时故障，所以将 ************ 加入swarm集群作为Worker，并且打上label使其仅用于部署这三个服务

```sh
docker swarm join-token worker
docker swarm join --token SWMTKN-1-...
docker node update --label-add ops=true <NODE_ID_OF_************>
docker node ls -q | xargs -n1 docker node inspect --format '{{.ID}} {{.Description.Hostname}} {{json .Spec.Labels}}'

docker node update --label-add app=true <NODE_ID_OF_OTHER>


docker tag grafana/promtail:3.5.1 docker-registry.local:5000/promtail:3.5.1
docker tag grafana/loki:3.5.1  docker-registry.local:5000/loki:3.5.1
docker tag grafana/grafana:12.0.1-security-01 docker-registry.local:5000/grafana:12.0.1-security-01
docker tag prom/prometheus:v3.4.1 docker-registry.local:5000/prometheus:v3.4.1


docker push docker-registry.local:5000/promtail:3.5.1
docker push docker-registry.local:5000/loki:3.5.1
docker push docker-registry.local:5000/grafana:12.0.1-security-01
docker push docker-registry.local:5000/prometheus:v3.4.1

docker rmi grafana/promtail:3.5.1
docker rmi grafana/loki:3.5.1
docker rmi grafana/grafana:12.0.1-security-01
docker rmi prom/prometheus:v3.4.1

```

- /data/prometheus/config/prometheus.yml

```yaml
# 全局配置
global:
  scrape_interval: 30s      # 每30秒抓取一次指标
  evaluation_interval: 30s  # 每30秒评估一次告警规则

scrape_configs:

  - job_name: 'node_exporter'
    static_configs:
      - targets:
          - '***********:9100'
          - '***********:9100'
          - '***********:9100'
          - '***********:9100'
          - '***********:9100'
          - '***********:9100'
          - '************:9100'
          - '************:9100'
          - '************:9100'
          - '************:9100'

  - job_name: 'swarm-services'
    dockerswarm_sd_configs:
      - host: unix:///var/run/docker.sock
        role: tasks

    relabel_configs:
      # - 保留运行中的任务
      - source_labels: [__meta_dockerswarm_task_state]
        regex: running
        action: keep

      # - 保留有 prometheus.io.scrape=true 标签的服务
      - source_labels: [__meta_dockerswarm_service_label_prometheus_io_scrape]
        regex: true
        action: keep

      # - 保留在 shared-net 网络中的服务
      - source_labels: [__meta_dockerswarm_network_name]
        regex: shared-net
        action: keep


      # - 添加容器所在主机名
      - source_labels: [__meta_dockerswarm_node_hostname]
        target_label: host

      # - 添加容器任务名
      - source_labels: [__meta_dockerswarm_task_id]
        target_label: container

      # - 从镜像提取简化 app 名称
      - source_labels: [__meta_dockerswarm_service_label_com_docker_stack_image]
        target_label: app
        regex: .*/([^:]+):.*
        replacement: $1
        action: replace

      # - 设置抓取路径（支持 prometheus.io.path 标签）
      - source_labels: [__meta_dockerswarm_task_label_prometheus_io_path]
        target_label: __metrics_path__
        regex: (.+)
        replacement: ${1}

      # - 正确拼接容器 IP 与端口（使用标签中的 prometheus.io.port）
      - source_labels: [__address__, __meta_dockerswarm_service_label_prometheus_io_port]
        regex: (.+):\d+;(\d+)
        replacement: ${1}:${2}
        target_label: __address__

  - job_name: 'file-sd'
    file_sd_configs:
      - files:
        - '/etc/prometheus/targets/*.json'

```

### swarm stack 网络

```sh
# docker network create --driver=overlay shared-net
docker network create --driver overlay --subnet=**********/16 --gateway=********** shared-net


docker node update --label-add ops=true <NODE_ID_OF_************>
docker node update --label-add app=true <NODE_ID_OF_OTHER>

docker node ls -q | xargs -n1 docker node inspect --format '{{.ID}} {{.Description.Hostname}} {{json .Spec.Labels}}'

```
