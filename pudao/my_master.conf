# =====================================================================
# 最终生产级 `my.cnf` - 适用于 4核16G MySQL 5.7 服务器
# =====================================================================

[client]
port = 3306
# 我们将使用新的、分离的目录结构。
socket = /data/mysql/run/mysql.sock

[mysqldump]
max_allowed_packet = 512M

[mysqld]
# --- 基础与用户设置 ---
# 为每台服务器手动设置。这对 MHA 至关重要。
# Master: server_id = 17, Slave: server_id = 18
server-id = 7
report_host=***********
user = mysql
port = 3306

# --- 路径与目录设置 (对新结构至关重要) ---
# 所有路径都指向数据盘挂载点 (/data/mysql)。
datadir = /data/mysql/data
tmpdir = /data/mysql/tmp
socket = /data/mysql/run/mysql.sock
pid-file = /data/mysql/run/mysql.pid

# --- 字符集 ---
character-set-server = utf8mb4
collation-server = utf8mb4_general_ci
init_connect = 'SET NAMES utf8mb4'

# --- 行为与限制 ---
lower_case_table_names = 1
skip-name-resolve = 1
explicit_defaults_for_timestamp = ON
sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION'

# --- 连接设置 (为 16GB 内存调优) ---
# 对于 16GB 内存的服务器，这是一个安全的起始值。
max_connections = 500
max_user_connections = 450
max_connect_errors = 99999999
# 对于有连接池的应用，一个较短的 wait_timeout 是有益的。
wait_timeout = 3600
interactive_timeout = 3600
# 根据 500 个连接数适当调整。
back_log = 150
# 防止客户端发送可能导致内存问题的巨大数据包。
max_allowed_packet = 64M

# --- 缓冲与缓存设置 (每个会话，对 16GB 内存至关重要) ---
# 这些值设置得比较保守，以防止大量并发连接导致内存耗尽。
read_buffer_size = 2M
read_rnd_buffer_size = 4M
sort_buffer_size = 4M
join_buffer_size = 4M
# 明确设置以禁用此已废弃的功能。
query_cache_size = 0
query_cache_type = 0
# 为复杂查询中创建的临时表设置适当的大小。
tmp_table_size = 64M
max_heap_table_size = 64M
# 一个好的线程缓存默认值。
thread_cache_size = 64
table_open_cache = 4096

# --- 日志 ---
log-error = /data/mysql/log/error.log
slow_query_log = 1
slow_query_log_file = /data/mysql/log/mysql-slow.log
long_query_time = 1
# 记录未使用索引的查询，对性能调优非常有帮助。
log_queries_not_using_indexes = 1
# 将所有死锁信息打印到错误日志中以便分析。
innodb_print_all_deadlocks = 1

# --- 二进制日志与复制 (启用GTID) ---
log-bin = /data/mysql/log/mysql-bin
binlog_format = ROW
# sync_binlog=1 对数据安全至关重要，确保每个事务都被写入磁盘。
sync_binlog = 1
expire_logs_days = 7
# 这对于从库未来也作为另一个从库的主库是必需的。
log_slave_updates = 1
# GTID 设置对现代复制和 MHA 至关重要。
gtid_mode = ON
enforce-gtid-consistency = ON
log_timestamps = SYSTEM
# 将仓库信息存储在表中比文件中更健壮。
master-info-repository = TABLE
relay-log-info-repository = TABLE
relay_log = /data/mysql/log/relay-bin
relay_log_recovery = ON
# 在受控环境中这是一个好习惯。
skip-slave-start = ON

# --- InnoDB 设置 (为 4C16G 调优) ---
default-storage-engine = InnoDB
# 这是对性能影响最大的单个参数。10G 对于 16G 服务器是一个很好的值。
innodb_buffer_pool_size = 10G
# 对于 10G 的缓冲池，8 个实例有助于减少争用。
innodb_buffer_pool_instances = 8
# 日志文件大小是写入性能和恢复时间之间的权衡。512M 是一个可靠的选择。
innodb_log_file_size = 512M
innodb_log_buffer_size = 16M
# O_DIRECT 是专用数据库服务器（使用硬件RAID或现代SSD）的标准配置。
innodb_flush_method = O_DIRECT
# 以牺牲一些写入性能为代价来保证ACID合规性。对生产环境至关重要。
innodb_flush_log_at_trx_commit = 1
# 将 I/O 负载分散到可用的核心上。
innodb_write_io_threads = 4
innodb_read_io_threads = 4
# 其他重要的生产设置。
innodb_file_per_table = 1
innodb_open_files = 4096
innodb_lock_wait_timeout = 60
innodb_strict_mode = 1
# 在高并发环境下有助于减少死锁。
innodb_autoinc_lock_mode = 2