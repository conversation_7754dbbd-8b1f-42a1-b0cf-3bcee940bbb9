## app

```sh
mkdir /opt/app/principal-in          ## 8001 9001
mkdir /opt/app/tako-in               ## 8002 9002
mkdir /opt/app/mflow-in              ## 8003 9003
mkdir /opt/app/jingway-in            ## 8004 9094
mkdir /opt/app/statistics-in         ## 8005 9095
mkdir /opt/app/argus-api             ## 8006 9006
mkdir /opt/app/gateway-api           ## 8007 9007

mkdir /opt/app/tako-api              ## 8008 9008
mkdir /opt/app/jingway-api           ## 8009 9099

mkdir /opt/app/warpdrive-in          ## 8010 9010
mkdir /opt/app/dnginj-ml             # 8011 9011
mkdir /opt/app/modelengine           ## 8012 9012
mkdir /opt/app/zeus                  ## 8013 9013
```

## java service

> generate-services.sh
```sh
#!/bin/bash

OUTPUT_DIR="./generated-services"
JAVA_BIN="/usr/bin/java"
SPRING_PROFILE="qa"

RUN_USER="root"
RUN_GROUP="root"

services=(
  "principal-in"
  "tako-in"
  "mflow-in"
  "jingway-in"
  "argus-api"
  "gateway-api"
  "jingway-api"
  "tako-api"
  "statistics-in"
  "warpdrive-in"
)

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

echo "Generating .service files to $OUTPUT_DIR..."

for service in "${services[@]}"; do
  # 判断是否为 API 服务
  if [[ "$service" == *"-api" ]]; then
    JAVA_OPTS="-Xms512m -Xmx2048m"
  else
    JAVA_OPTS="-Xms256m -Xmx512m"
  fi

  # 拼接文件名
  SERVICE_FILE="$OUTPUT_DIR/$service.service"

  # 拼接 systemd 文件内容
  cat <<EOF > "$SERVICE_FILE"
[Unit]
Description=$service
After=network.target

[Service]
User=$RUN_USER
Group=$RUN_GROUP
WorkingDirectory=/opt/app/$service
Environment=SPRING_PROFILES_ACTIVE=$SPRING_PROFILE
Environment="JAVA_OPTS=$JAVA_OPTS"
ExecStart=$JAVA_BIN \$JAVA_OPTS -jar $service.jar
SuccessExitStatus=143
Restart=on-failure
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF

  echo "Created: $SERVICE_FILE"
done

echo "✅ All service files generated."

```

```sh
cp generated-services/*.service /etc/systemd/system/
systemctl daemon-reload
```

## zeus

```sh
tar -czf zeus.tgz -C static .
```

- nginx

> zeus.conf

```nginx
server {
    listen 8013 default;
    gzip_types application/x-javascript text/css;
    fastcgi_intercept_errors on;
    client_max_body_size 5m;
    error_page 404 /404;

    location ~* /zeus/.*\.html$ {
        add_header Cache-Control "no-store, no-cache, private";
        root /opt/app/zeus/;
    }

    location = /statics {
        try_files $uri $uri/ /zeus/index.html;
    }

    location = /favicon.ico {
        try_files $uri $uri/ /zeus/favicon.ico;
    }

    location ^~ /statics/ {
        try_files $uri $uri/ /zeus/index.html;
    }

    location /api/urule-web {
        rewrite /api/urule-web/(.+)$ /$1 break;
        proxy_pass http://localhost:8007;
    }

    location /api {
        if ($request_uri ~* ^/api/(.*)$) {
            proxy_pass http://localhost:8007/$1;
        }
    }

    location = / {
        rewrite .* statics redirect;
    }

    location / {
        root /opt/app/zeus/;
    }
}
```

## modelengine

```sh
tar -czf modelengine.tgz -C static .
```

- nginx

> modelengine.conf

```nginx
server {
  listen 8012 default;
  gzip_types application/x-javascript text/css;
  fastcgi_intercept_errors on;
  client_max_body_size 5m;
  error_page 404 /404;

  location ~* /modelengine/.*\.html$ {
      add_header Cache-Control "no-store, no-cache, private";
      root /opt/app/modelengine/;
  }

  location = /statics {
      try_files $uri $uri/ /modelengine/index.html;
  }

  location = /favicon.ico {
      try_files $uri $uri/ /modelengine/favicon.ico;
  }

  location ^~ /statics/ {
      try_files $uri $uri/ /modelengine/index.html;
  }

  location /api/urule-web {
      rewrite /api/urule-web/(.+)$ /$1 break;
      proxy_pass http://localhost:8010;
  }

  location /api {
      if ($request_uri ~* ^/api/(.*)$) {
          proxy_pass http://localhost:8010/$1;
      }
  }

  location = / {
      rewrite .* statics redirect;
  }

  location / {
      root /opt/app/modelengine/;
  }
}
```


### yapi

```sh

tee /etc/systemd/system/yapi.service <<'EOF' > /dev/null

[Unit]
# 服务的描述
Description=YApi Mock Platform Service
# 确保在网络准备就绪后才启动
After=network.target

[Service]
Type=simple
WorkingDirectory=/opt/app/yapi

ExecStart=/usr/bin/npm run start

# 失败时自动重启策略
Restart=on-failure
# 每次重启间隔5秒
RestartSec=5s

# 限制：为服务设置一个较高的文件打开数限制，防止因连接数过多而出错
LimitNOFILE=65536

[Install]
# 定义服务所属的目标级别，multi-user.target 意味着在多用户模式下启动
WantedBy=multi-user.target

EOF

```